#!/usr/bin/env python3
"""
自动下载并安装MinGW编译器到env目录
"""

import os
import sys
import urllib.request
import zipfile
import shutil
from pathlib import Path

def download_file(url, filename):
    """下载文件"""
    print(f"正在下载: {url}")
    print(f"保存到: {filename}")
    
    try:
        def progress_hook(block_num, block_size, total_size):
            downloaded = block_num * block_size
            if total_size > 0:
                percent = min(100, downloaded * 100 / total_size)
                print(f"\r下载进度: {percent:.1f}% ({downloaded:,}/{total_size:,} 字节)", end='')
            else:
                print(f"\r已下载: {downloaded:,} 字节", end='')
        
        urllib.request.urlretrieve(url, filename, progress_hook)
        print("\n✓ 下载完成")
        return True
    except Exception as e:
        print(f"\n✗ 下载失败: {e}")
        return False

def extract_zip(zip_file, extract_to):
    """解压ZIP文件"""
    print(f"正在解压: {zip_file}")
    print(f"解压到: {extract_to}")
    
    try:
        with zipfile.ZipFile(zip_file, 'r') as zip_ref:
            zip_ref.extractall(extract_to)
        print("✓ 解压完成")
        return True
    except Exception as e:
        print(f"✗ 解压失败: {e}")
        return False

def setup_mingw():
    """设置MinGW编译器"""
    print("🔧 设置MinGW编译器")
    print("="*50)
    
    # 创建env目录
    env_dir = Path("env")
    env_dir.mkdir(exist_ok=True)
    
    # MinGW下载URL (使用轻量级版本)
    mingw_url = "https://github.com/niXman/mingw-builds-binaries/releases/download/13.2.0-rt_v11-rev0/x86_64-13.2.0-release-posix-seh-msvcrt-rt_v11-rev0.7z"
    mingw_filename = env_dir / "mingw.7z"
    
    # 如果已经存在编译器，跳过下载
    gcc_exe = env_dir / "mingw64" / "bin" / "gcc.exe"
    if gcc_exe.exists():
        print("✓ MinGW编译器已存在")
        return str(gcc_exe)
    
    print("MinGW编译器不存在，开始下载...")
    
    # 尝试使用7z格式，如果失败则使用zip格式
    try:
        # 先尝试下载预编译的MinGW
        success = download_file(mingw_url, mingw_filename)
        
        if success:
            # 尝试解压7z文件
            try:
                import py7zr
                with py7zr.SevenZipFile(mingw_filename, mode='r') as z:
                    z.extractall(env_dir)
                print("✓ 7z解压完成")
            except ImportError:
                print("⚠ 需要安装py7zr库来解压7z文件")
                print("正在安装py7zr...")
                os.system("pip install py7zr")
                
                import py7zr
                with py7zr.SevenZipFile(mingw_filename, mode='r') as z:
                    z.extractall(env_dir)
                print("✓ 7z解压完成")
            
            # 清理下载文件
            mingw_filename.unlink()
            
            if gcc_exe.exists():
                print(f"✓ MinGW安装成功: {gcc_exe}")
                return str(gcc_exe)
    
    except Exception as e:
        print(f"⚠ 7z下载失败: {e}")
    
    # 备用方案：下载更小的MinGW版本
    print("尝试备用下载方案...")
    
    # 使用TDM-GCC的轻量级版本
    backup_url = "https://github.com/jmeubank/tdm-gcc/releases/download/v10.3.0-tdm64-2/tdm64-gcc-10.3.0-2.exe"
    backup_filename = env_dir / "tdm-gcc-installer.exe"
    
    success = download_file(backup_url, backup_filename)
    
    if success:
        print("✓ 下载了TDM-GCC安装程序")
        print("请手动运行安装程序:")
        print(f"  {backup_filename.absolute()}")
        print("安装时选择安装到: env/mingw64/")
        return None
    
    print("✗ 所有下载方案都失败了")
    return None

def create_portable_mingw():
    """创建便携式MinGW"""
    print("🔧 创建便携式MinGW")
    print("="*50)
    
    env_dir = Path("env")
    mingw_dir = env_dir / "mingw64"
    
    # 创建基本目录结构
    (mingw_dir / "bin").mkdir(parents=True, exist_ok=True)
    (mingw_dir / "include").mkdir(parents=True, exist_ok=True)
    (mingw_dir / "lib").mkdir(parents=True, exist_ok=True)
    
    # 下载基本的编译工具
    tools = [
        ("https://github.com/mstorsjo/llvm-mingw/releases/download/20231128/llvm-mingw-20231128-msvcrt-x86_64.zip", "llvm-mingw.zip")
    ]
    
    for url, filename in tools:
        zip_path = env_dir / filename
        
        if download_file(url, zip_path):
            if extract_zip(zip_path, env_dir):
                # 移动文件到正确位置
                extracted_dir = env_dir / "llvm-mingw-20231128-msvcrt-x86_64"
                if extracted_dir.exists():
                    # 复制bin目录
                    src_bin = extracted_dir / "bin"
                    dst_bin = mingw_dir / "bin"
                    
                    if src_bin.exists():
                        for file in src_bin.glob("*"):
                            if file.is_file():
                                shutil.copy2(file, dst_bin)
                    
                    # 清理
                    shutil.rmtree(extracted_dir, ignore_errors=True)
                    zip_path.unlink()
                    
                    gcc_exe = mingw_dir / "bin" / "clang.exe"
                    if gcc_exe.exists():
                        # 创建gcc.exe的符号链接
                        gcc_link = mingw_dir / "bin" / "gcc.exe"
                        if not gcc_link.exists():
                            shutil.copy2(gcc_exe, gcc_link)
                        
                        print(f"✓ 便携式编译器安装成功: {gcc_link}")
                        return str(gcc_link)
    
    return None

def main():
    print("🎯 MinGW编译器自动安装")
    print("="*50)
    
    # 检查是否已有编译器
    env_dir = Path("env")
    possible_compilers = [
        env_dir / "mingw64" / "bin" / "gcc.exe",
        env_dir / "mingw64" / "bin" / "clang.exe",
        env_dir / "bin" / "gcc.exe"
    ]
    
    for compiler in possible_compilers:
        if compiler.exists():
            print(f"✓ 找到现有编译器: {compiler}")
            return str(compiler)
    
    print("未找到编译器，开始安装...")
    
    # 方法1: 尝试安装MinGW
    compiler = setup_mingw()
    if compiler:
        return compiler
    
    # 方法2: 创建便携式版本
    compiler = create_portable_mingw()
    if compiler:
        return compiler
    
    # 方法3: 提供手动安装指导
    print("\n" + "="*50)
    print("⚠ 自动安装失败，请手动安装编译器")
    print("="*50)
    
    print("\n推荐方案:")
    print("1. 下载MSYS2: https://www.msys2.org/")
    print("2. 安装后运行以下命令:")
    print("   pacman -S mingw-w64-x86_64-gcc")
    print("   pacman -S mingw-w64-x86_64-make")
    print("3. 将 C:\\msys64\\mingw64\\bin 添加到系统PATH")
    
    print("\n或者:")
    print("1. 下载TDM-GCC: https://jmeubank.github.io/tdm-gcc/")
    print("2. 安装到 env/mingw64/ 目录")
    
    return None

if __name__ == "__main__":
    try:
        compiler = main()
        if compiler:
            print(f"\n🎉 编译器安装成功: {compiler}")
            
            # 测试编译器
            import subprocess
            try:
                result = subprocess.run([compiler, "--version"], 
                                      capture_output=True, text=True, check=True)
                print(f"编译器版本: {result.stdout.split()[0] if result.stdout else 'Unknown'}")
            except:
                print("⚠ 编译器可能需要额外配置")
        else:
            print("\n❌ 编译器安装失败")
            print("请参考上述手动安装指导")
    except KeyboardInterrupt:
        print("\n\n⚠ 安装被用户中断")
    except Exception as e:
        print(f"\n❌ 安装过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
