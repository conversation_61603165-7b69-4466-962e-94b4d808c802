#!/usr/bin/env python3
"""
原始RNNoise训练脚本包装器
使用原封不动的RNNoise训练代码进行模型训练
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path

def check_features_file(features_file):
    """检查特征文件"""
    if not os.path.exists(features_file):
        raise FileNotFoundError(f"特征文件不存在: {features_file}")
    
    file_size = os.path.getsize(features_file)
    print(f"特征文件: {features_file}")
    print(f"文件大小: {file_size / (1024*1024):.2f} MB")
    
    # 计算特征数量 (98维特征，每个float32占4字节)
    feature_count = file_size // (98 * 4)
    print(f"特征帧数: {feature_count}")
    
    return True

def create_output_directory(output_dir):
    """创建输出目录"""
    os.makedirs(output_dir, exist_ok=True)
    checkpoints_dir = os.path.join(output_dir, 'checkpoints')
    os.makedirs(checkpoints_dir, exist_ok=True)
    return checkpoints_dir

def run_training(features_file, output_dir, args):
    """运行原始训练脚本"""
    print("=== 开始RNNoise训练 ===")
    print(f"特征文件: {features_file}")
    print(f"输出目录: {output_dir}")
    print(f"训练轮次: {args.epochs}")
    print(f"批次大小: {args.batch_size}")
    print(f"学习率: {args.lr}")
    print(f"序列长度: {args.sequence_length}")
    
    # 构建训练命令
    cmd = [
        sys.executable, 'train_rnnoise.py',
        features_file,
        output_dir,
        '--epochs', str(args.epochs),
        '--batch-size', str(args.batch_size),
        '--lr', str(args.lr),
        '--sequence-length', str(args.sequence_length),
        '--lr-decay', str(args.lr_decay),
        '--gamma', str(args.gamma),
        '--cond-size', str(args.cond_size),
        '--gru-size', str(args.gru_size),
        '--suffix', args.suffix
    ]
    
    # 添加可选参数
    if args.initial_checkpoint:
        cmd.extend(['--initial-checkpoint', args.initial_checkpoint])
    
    if args.sparse:
        cmd.append('--sparse')
    
    if args.cuda_visible_devices:
        cmd.extend(['--cuda-visible-devices', args.cuda_visible_devices])
    
    print(f"执行命令: {' '.join(cmd)}")
    print("-" * 60)
    
    # 执行训练
    try:
        result = subprocess.run(cmd, check=True, cwd=os.path.dirname(__file__))
        print("-" * 60)
        print("✓ 训练完成！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 训练失败: {e}")
        return False

def verify_training_output(output_dir, epochs, suffix):
    """验证训练输出"""
    checkpoints_dir = os.path.join(output_dir, 'checkpoints')
    
    print("\n=== 验证训练输出 ===")
    
    # 检查检查点文件
    checkpoint_files = []
    for epoch in range(1, epochs + 1):
        checkpoint_file = os.path.join(checkpoints_dir, f'rnnoise{suffix}_{epoch}.pth')
        if os.path.exists(checkpoint_file):
            checkpoint_files.append(checkpoint_file)
            file_size = os.path.getsize(checkpoint_file)
            print(f"✓ Epoch {epoch}: {checkpoint_file} ({file_size / (1024*1024):.2f} MB)")
        else:
            print(f"✗ Epoch {epoch}: 检查点文件缺失")
    
    if checkpoint_files:
        print(f"\n✓ 成功生成 {len(checkpoint_files)} 个检查点文件")
        print(f"最终模型: {checkpoint_files[-1]}")
        return checkpoint_files[-1]
    else:
        print("\n✗ 没有找到任何检查点文件")
        return None

def main():
    parser = argparse.ArgumentParser(description='原始RNNoise训练脚本')
    
    # 必需参数
    parser.add_argument('--features', type=str, 
                       default='features/original_training_features.f32',
                       help='特征文件路径 (默认: features/original_training_features.f32)')
    parser.add_argument('--output', type=str, 
                       default='model_output',
                       help='模型输出目录 (默认: model_output)')
    
    # 训练参数
    parser.add_argument('--epochs', type=int, default=200,
                       help='训练轮次 (默认: 200)')
    parser.add_argument('--batch-size', type=int, default=128,
                       help='批次大小 (默认: 128)')
    parser.add_argument('--lr', type=float, default=1e-3,
                       help='学习率 (默认: 1e-3)')
    parser.add_argument('--sequence-length', type=int, default=2000,
                       help='序列长度 (默认: 2000)')
    parser.add_argument('--lr-decay', type=float, default=5e-5,
                       help='学习率衰减 (默认: 5e-5)')
    parser.add_argument('--gamma', type=float, default=0.25,
                       help='感知指数 (默认: 0.25)')
    
    # 模型参数
    parser.add_argument('--cond-size', type=int, default=128,
                       help='条件层大小 (默认: 128)')
    parser.add_argument('--gru-size', type=int, default=384,
                       help='GRU层大小 (默认: 384)')
    
    # 可选参数
    parser.add_argument('--suffix', type=str, default='_custom',
                       help='模型名称后缀 (默认: _custom)')
    parser.add_argument('--initial-checkpoint', type=str,
                       help='初始检查点文件路径')
    parser.add_argument('--sparse', action='store_true',
                       help='启用稀疏化训练')
    parser.add_argument('--cuda-visible-devices', type=str,
                       help='CUDA可见设备')
    
    args = parser.parse_args()
    
    try:
        # 检查特征文件
        check_features_file(args.features)
        
        # 创建输出目录
        checkpoints_dir = create_output_directory(args.output)
        
        # 运行训练
        success = run_training(args.features, args.output, args)
        
        if success:
            # 验证输出
            final_model = verify_training_output(args.output, args.epochs, args.suffix)
            
            if final_model:
                print(f"\n🎉 训练成功完成！")
                print(f"最终模型: {final_model}")
                print(f"检查点目录: {checkpoints_dir}")
                print("\n下一步:")
                print(f"1. 运行验证脚本: python validate_model.py --model {final_model}")
                print(f"2. 转换为C代码: python dump_rnnoise_weights.py --quantize {final_model} output_c")
            else:
                print("\n⚠️ 训练完成但输出验证失败")
        else:
            print("\n❌ 训练失败")
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
