/* 
 * 自定义RNNoise模型测试程序
 * 使用您训练的模型进行音频降噪测试
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include "include/rnnoise.h"

#define FRAME_SIZE 480
#define SAMPLE_RATE 48000

/* 音频文件处理函数 */
int read_pcm_file(const char* filename, short** data, int* length) {
    FILE* file = fopen(filename, "rb");
    if (!file) {
        printf("错误: 无法打开文件 %s\n", filename);
        return 0;
    }
    
    // 获取文件大小
    fseek(file, 0, SEEK_END);
    long file_size = ftell(file);
    fseek(file, 0, SEEK_SET);
    
    *length = file_size / sizeof(short);
    *data = (short*)malloc(file_size);
    
    if (!*data) {
        printf("错误: 内存分配失败\n");
        fclose(file);
        return 0;
    }
    
    size_t read_count = fread(*data, sizeof(short), *length, file);
    fclose(file);
    
    printf("读取PCM文件: %s, 样本数: %d, 时长: %.2f秒\n", 
           filename, *length, (float)*length / SAMPLE_RATE);
    
    return read_count == *length;
}

int write_pcm_file(const char* filename, const short* data, int length) {
    FILE* file = fopen(filename, "wb");
    if (!file) {
        printf("错误: 无法创建文件 %s\n", filename);
        return 0;
    }
    
    size_t written = fwrite(data, sizeof(short), length, file);
    fclose(file);
    
    printf("写入PCM文件: %s, 样本数: %d\n", filename, length);
    return written == length;
}

/* 音频质量评估函数 */
void analyze_audio_quality(const short* original, const short* denoised, int length) {
    double original_energy = 0.0, denoised_energy = 0.0;
    double noise_reduction = 0.0;
    int silent_frames = 0;
    
    for (int i = 0; i < length; i++) {
        double orig = original[i] / 32768.0;
        double den = denoised[i] / 32768.0;
        
        original_energy += orig * orig;
        denoised_energy += den * den;
        
        if (abs(denoised[i]) < 100) {
            silent_frames++;
        }
    }
    
    original_energy = sqrt(original_energy / length);
    denoised_energy = sqrt(denoised_energy / length);
    
    if (original_energy > 0) {
        noise_reduction = 20.0 * log10(original_energy / (denoised_energy + 1e-10));
    }
    
    printf("\n=== 音频质量分析 ===\n");
    printf("原始音频RMS: %.4f\n", original_energy);
    printf("降噪音频RMS: %.4f\n", denoised_energy);
    printf("降噪效果: %.2f dB\n", noise_reduction);
    printf("静音帧比例: %.2f%%\n", (float)silent_frames / length * 100);
}

/* 主测试函数 */
int test_rnnoise_model(const char* input_file, const char* output_file) {
    printf("\n=== RNNoise自定义模型测试 ===\n");
    
    // 读取输入音频
    short* input_data;
    int input_length;
    if (!read_pcm_file(input_file, &input_data, &input_length)) {
        return 1;
    }
    
    // 创建RNNoise状态
    DenoiseState* st = rnnoise_create(NULL);  // 使用默认模型
    if (!st) {
        printf("错误: 无法创建RNNoise状态\n");
        free(input_data);
        return 1;
    }
    
    printf("RNNoise状态创建成功\n");
    printf("帧大小: %d 样本\n", rnnoise_get_frame_size());
    
    // 分配输出缓冲区
    short* output_data = (short*)malloc(input_length * sizeof(short));
    if (!output_data) {
        printf("错误: 输出缓冲区分配失败\n");
        rnnoise_destroy(st);
        free(input_data);
        return 1;
    }
    
    // 处理音频帧
    int processed_frames = 0;
    int total_frames = input_length / FRAME_SIZE;
    float x[FRAME_SIZE];
    
    printf("开始处理音频，总帧数: %d\n", total_frames);
    
    for (int frame = 0; frame < total_frames; frame++) {
        // 转换为浮点数
        for (int i = 0; i < FRAME_SIZE; i++) {
            x[i] = input_data[frame * FRAME_SIZE + i];
        }
        
        // 执行降噪
        float vad_prob = rnnoise_process_frame(st, x, x);
        
        // 转换回整数
        for (int i = 0; i < FRAME_SIZE; i++) {
            int sample = (int)(x[i] + 0.5f);
            if (sample > 32767) sample = 32767;
            if (sample < -32768) sample = -32768;
            output_data[frame * FRAME_SIZE + i] = sample;
        }
        
        processed_frames++;
        
        // 显示进度
        if (frame % 100 == 0) {
            printf("处理进度: %d/%d 帧 (%.1f%%), VAD: %.3f\n", 
                   frame, total_frames, (float)frame / total_frames * 100, vad_prob);
        }
    }
    
    printf("音频处理完成，处理了 %d 帧\n", processed_frames);
    
    // 分析音频质量
    analyze_audio_quality(input_data, output_data, processed_frames * FRAME_SIZE);
    
    // 写入输出文件
    int output_length = processed_frames * FRAME_SIZE;
    if (write_pcm_file(output_file, output_data, output_length)) {
        printf("降噪音频已保存到: %s\n", output_file);
    }
    
    // 清理资源
    rnnoise_destroy(st);
    free(input_data);
    free(output_data);
    
    return 0;
}

int main(int argc, char** argv) {
    printf("RNNoise 自定义模型测试程序\n");
    printf("编译时间: %s %s\n", __DATE__, __TIME__);
    
    if (argc != 3) {
        printf("用法: %s <输入PCM文件> <输出PCM文件>\n", argv[0]);
        printf("示例: %s noise_voice/test.pcm output_clean.pcm\n", argv[0]);
        printf("\n注意: 输入文件必须是48kHz, 16位, 单声道的PCM格式\n");
        return 1;
    }
    
    const char* input_file = argv[1];
    const char* output_file = argv[2];
    
    printf("输入文件: %s\n", input_file);
    printf("输出文件: %s\n", output_file);
    
    int result = test_rnnoise_model(input_file, output_file);
    
    if (result == 0) {
        printf("\n✓ 测试完成！\n");
        printf("您可以使用以下命令将PCM转换为WAV:\n");
        printf("ffmpeg -f s16le -ar 48000 -ac 1 -i %s %s.wav\n", output_file, output_file);
    } else {
        printf("\n✗ 测试失败！\n");
    }
    
    return result;
}
