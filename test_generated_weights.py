#!/usr/bin/env python3
"""
验证生成的RNNoise权重文件
"""

import os
import sys
import subprocess
import wave
import numpy as np
import matplotlib.pyplot as plt

def check_files_exist():
    """检查必要的文件是否存在"""
    files_to_check = [
        "rnnoise_c.c",
        "rnnoise_c.h", 
        "examples/rnnoise_demo.c",
        "include/rnnoise.h",
        "temp_16k.wav"
    ]
    
    missing_files = []
    for file in files_to_check:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少以下文件:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    print("✅ 所有必要文件都存在")
    return True

def compile_test_program():
    """编译测试程序"""
    try:
        # 编译命令
        cmd = [
            "gcc", "-o", "test_rnnoise",
            "examples/rnnoise_demo.c", 
            "src/*.c",
            "-Iinclude", 
            "-lm"
        ]
        
        print("🔨 编译测试程序...")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 编译成功")
            return True
        else:
            print("❌ 编译失败:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 编译出错: {e}")
        return False

def test_audio_processing():
    """测试音频处理"""
    try:
        # 检查输入文件
        if not os.path.exists("temp_16k.wav"):
            print("❌ 测试音频文件不存在")
            return False
        
        # 运行降噪测试
        print("🎵 测试音频降噪...")
        cmd = ["./test_rnnoise", "temp_16k.wav", "output_denoised.wav"]
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 音频处理成功")
            
            # 检查输出文件
            if os.path.exists("output_denoised.wav"):
                # 分析音频文件
                analyze_audio_files()
                return True
            else:
                print("❌ 输出文件未生成")
                return False
        else:
            print("❌ 音频处理失败:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 音频处理出错: {e}")
        return False

def analyze_audio_files():
    """分析音频文件"""
    try:
        print("\n📊 音频文件分析:")
        
        # 分析原始文件
        with wave.open("temp_16k.wav", 'rb') as wav_in:
            frames_in = wav_in.getnframes()
            sample_rate_in = wav_in.getframerate()
            duration_in = frames_in / sample_rate_in
            print(f"   原始文件: {duration_in:.2f}秒, {sample_rate_in}Hz")
        
        # 分析输出文件
        with wave.open("output_denoised.wav", 'rb') as wav_out:
            frames_out = wav_out.getnframes()
            sample_rate_out = wav_out.getframerate()
            duration_out = frames_out / sample_rate_out
            print(f"   降噪文件: {duration_out:.2f}秒, {sample_rate_out}Hz")
        
        # 检查文件大小
        size_in = os.path.getsize("temp_16k.wav")
        size_out = os.path.getsize("output_denoised.wav")
        print(f"   文件大小: {size_in/1024:.1f}KB -> {size_out/1024:.1f}KB")
        
        print("✅ 音频分析完成")
        
    except Exception as e:
        print(f"❌ 音频分析出错: {e}")

def main():
    """主函数"""
    print("🧪 RNNoise 权重文件测试")
    print("=" * 50)
    
    # 1. 检查文件
    if not check_files_exist():
        return
    
    # 2. 编译程序
    if not compile_test_program():
        return
    
    # 3. 测试音频处理
    if not test_audio_processing():
        return
    
    print("\n🎉 所有测试通过！你的权重文件工作正常。")
    print("\n📝 下一步:")
    print("   - 可以播放 output_denoised.wav 听降噪效果")
    print("   - 可以尝试其他音频文件进行测试")
    print("   - 可以集成到你的应用程序中")

if __name__ == "__main__":
    main() 