#!/usr/bin/env python3
"""
RNNoise C语言模型测试脚本
专门针对您的模型和音频文件
"""

import os
import sys
import subprocess
import shutil
import platform
from pathlib import Path

class CModelTester:
    """C模型测试器"""
    
    def __init__(self):
        self.current_dir = Path(".")
        self.model_dir = Path("rnnoise_c")
        self.test_audio = Path("3.wav")
        self.root_dir = Path("../..")  # 回到项目根目录
        
        self.is_windows = platform.system().lower() == 'windows'
        self.exe_ext = '.exe' if self.is_windows else ''
        
        print(f"操作系统: {platform.system()}")
        print(f"当前目录: {os.getcwd()}")
        print(f"模型目录: {self.model_dir}")
        print(f"测试音频: {self.test_audio}")
    
    def check_files(self):
        """检查必要文件"""
        print("\n=== 检查文件 ===")
        
        files_to_check = [
            ("C模型头文件", self.model_dir / "rnnoise_data.h"),
            ("C模型数据文件", self.model_dir / "rnnoise_data.c"),
            ("测试音频", self.test_audio),
            ("RNNoise头文件", self.root_dir / "include/rnnoise.h"),
            ("示例程序", self.root_dir / "examples/rnnoise_demo.c"),
        ]
        
        all_exist = True
        for name, path in files_to_check:
            if path.exists():
                size = path.stat().st_size
                print(f"✓ {name}: {path} ({size} 字节)")
            else:
                print(f"✗ {name}: {path} (不存在)")
                all_exist = False
        
        return all_exist
    
    def check_compiler(self):
        """检查编译器"""
        print("\n=== 检查编译器 ===")
        
        compilers = ["gcc", "clang", "cl"]
        
        for compiler in compilers:
            try:
                result = subprocess.run([compiler, "--version"], 
                                      capture_output=True, text=True, check=True)
                print(f"✓ 找到编译器: {compiler}")
                print(f"  版本信息: {result.stdout.split()[0] if result.stdout else 'Unknown'}")
                return compiler
            except (subprocess.CalledProcessError, FileNotFoundError):
                continue
        
        print("✗ 未找到可用的编译器")
        print("请安装以下编译器之一:")
        print("  - GCC: https://www.mingw-w64.org/ (Windows)")
        print("  - Visual Studio Build Tools (Windows)")
        print("  - Xcode Command Line Tools (macOS)")
        return None
    
    def prepare_build_environment(self):
        """准备编译环境"""
        print("\n=== 准备编译环境 ===")
        
        # 复制模型文件到src目录
        src_dir = self.root_dir / "src"
        
        if not src_dir.exists():
            print(f"✗ src目录不存在: {src_dir}")
            return False
        
        # 备份原始文件
        original_c = src_dir / "rnnoise_data.c"
        if original_c.exists():
            backup_c = src_dir / "rnnoise_data.c.backup"
            shutil.copy2(original_c, backup_c)
            print("✓ 备份原始模型文件")
        
        # 复制自定义模型文件
        custom_h = self.model_dir / "rnnoise_data.h"
        custom_c = self.model_dir / "rnnoise_data.c"
        
        if custom_h.exists() and custom_c.exists():
            shutil.copy2(custom_h, src_dir / "rnnoise_data.h")
            shutil.copy2(custom_c, src_dir / "rnnoise_data.c")
            print("✓ 复制自定义模型文件到src目录")
            return True
        else:
            print("✗ 自定义模型文件不完整")
            return False
    
    def compile_test_program(self, compiler):
        """编译测试程序"""
        print("\n=== 编译测试程序 ===")
        
        # 切换到项目根目录
        original_cwd = os.getcwd()
        os.chdir(self.root_dir)
        
        try:
            # 编译参数
            cflags = ["-O3", "-Wall", "-std=c99"]
            includes = ["-Iinclude", "-Isrc"]
            libs = ["-lm"] if not self.is_windows else []
            
            # 源文件
            sources = [
                "src/denoise.c",
                "src/rnn.c",
                "src/pitch.c", 
                "src/kiss_fft.c",
                "src/celt_lpc.c",
                "src/nnet.c",
                "src/nnet_default.c",
                "src/parse_lpcnet_weights.c",
                "src/rnnoise_data.c",
                "src/rnnoise_tables.c"
            ]
            
            # 检查源文件
            missing = [s for s in sources if not Path(s).exists()]
            if missing:
                print(f"✗ 缺少源文件: {missing}")
                return False
            
            # 编译对象文件
            print("编译对象文件...")
            compile_cmd = [compiler] + cflags + includes + ["-c"] + sources
            result = subprocess.run(compile_cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                print(f"✗ 编译失败: {result.stderr}")
                return False
            
            print("✓ 对象文件编译成功")
            
            # 编译示例程序
            print("编译示例程序...")
            objects = [s.replace('.c', '.o').replace('src/', '') for s in sources]
            demo_cmd = [compiler] + cflags + includes + ["-o", f"rnnoise_demo{self.exe_ext}",
                       "examples/rnnoise_demo.c"] + objects + libs
            
            result = subprocess.run(demo_cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                print(f"✗ 示例程序编译失败: {result.stderr}")
                return False
            
            print(f"✓ 示例程序编译成功: rnnoise_demo{self.exe_ext}")
            return True
            
        finally:
            os.chdir(original_cwd)
    
    def convert_audio(self):
        """转换音频格式"""
        print("\n=== 转换音频格式 ===")
        
        # 查找ffmpeg
        ffmpeg_paths = [
            self.root_dir / "env/ffmpeg.exe",
            "ffmpeg.exe",
            "ffmpeg"
        ]
        
        ffmpeg = None
        for path in ffmpeg_paths:
            if Path(path).exists() or shutil.which(str(path)):
                ffmpeg = str(path)
                break
        
        if not ffmpeg:
            print("✗ 未找到ffmpeg")
            return None
        
        print(f"使用ffmpeg: {ffmpeg}")
        
        # 转换命令
        input_wav = self.test_audio
        output_pcm = "test_input.pcm"
        
        cmd = [
            ffmpeg,
            "-i", str(input_wav),
            "-f", "s16le",
            "-ar", "48000",
            "-ac", "1",
            "-y",
            output_pcm
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            print(f"✓ 音频转换成功: {input_wav} -> {output_pcm}")
            return output_pcm
        except subprocess.CalledProcessError as e:
            print(f"✗ 音频转换失败: {e.stderr}")
            return None
    
    def run_denoise_test(self, input_pcm):
        """运行降噪测试"""
        print("\n=== 运行降噪测试 ===")
        
        # 切换到项目根目录
        original_cwd = os.getcwd()
        os.chdir(self.root_dir)
        
        try:
            program = f"rnnoise_demo{self.exe_ext}"
            output_pcm = "output_denoised.pcm"
            
            if not Path(program).exists():
                print(f"✗ 程序不存在: {program}")
                return False
            
            # 复制输入文件到根目录
            input_path = Path("torch/rnnoise") / input_pcm
            if input_path.exists():
                shutil.copy2(input_path, input_pcm)
            
            cmd = [f"./{program}", input_pcm, output_pcm]
            
            print(f"执行命令: {' '.join(cmd)}")
            
            import time
            start_time = time.time()
            result = subprocess.run(cmd, capture_output=True, text=True)
            end_time = time.time()
            
            if result.returncode == 0:
                processing_time = end_time - start_time
                output_size = Path(output_pcm).stat().st_size if Path(output_pcm).exists() else 0
                
                print(f"✓ 降噪测试成功")
                print(f"  处理时间: {processing_time:.2f}秒")
                print(f"  输出大小: {output_size} 字节")
                
                # 转换结果为WAV
                self.convert_result_to_wav(output_pcm)
                
                return True
            else:
                print(f"✗ 降噪测试失败: {result.stderr}")
                return False
                
        finally:
            os.chdir(original_cwd)
    
    def convert_result_to_wav(self, pcm_file):
        """将PCM结果转换为WAV"""
        print("\n=== 转换结果为WAV ===")
        
        # 查找ffmpeg
        ffmpeg_paths = ["env/ffmpeg.exe", "ffmpeg.exe", "ffmpeg"]
        ffmpeg = None
        
        for path in ffmpeg_paths:
            if Path(path).exists() or shutil.which(path):
                ffmpeg = path
                break
        
        if not ffmpeg:
            print("✗ 未找到ffmpeg，跳过WAV转换")
            return
        
        wav_file = pcm_file.replace('.pcm', '.wav')
        
        cmd = [
            ffmpeg,
            "-f", "s16le",
            "-ar", "48000", 
            "-ac", "1",
            "-i", pcm_file,
            "-y",
            wav_file
        ]
        
        try:
            subprocess.run(cmd, capture_output=True, text=True, check=True)
            print(f"✓ 转换成功: {pcm_file} -> {wav_file}")
        except subprocess.CalledProcessError as e:
            print(f"✗ 转换失败: {e.stderr}")
    
    def run_full_test(self):
        """运行完整测试"""
        print("🎯 RNNoise C语言模型完整测试")
        print("="*50)
        
        # 步骤1: 检查文件
        if not self.check_files():
            print("\n❌ 必要文件缺失")
            return False
        
        # 步骤2: 检查编译器
        compiler = self.check_compiler()
        if not compiler:
            print("\n❌ 编译器不可用")
            return False
        
        # 步骤3: 准备编译环境
        if not self.prepare_build_environment():
            print("\n❌ 编译环境准备失败")
            return False
        
        # 步骤4: 编译程序
        if not self.compile_test_program(compiler):
            print("\n❌ 程序编译失败")
            return False
        
        # 步骤5: 转换音频
        input_pcm = self.convert_audio()
        if not input_pcm:
            print("\n❌ 音频转换失败")
            return False
        
        # 步骤6: 运行测试
        if not self.run_denoise_test(input_pcm):
            print("\n❌ 降噪测试失败")
            return False
        
        print("\n" + "="*50)
        print("🎉 测试完成！")
        print("="*50)
        
        print("\n📁 生成的文件:")
        output_files = ["test_input.pcm", "output_denoised.pcm", "output_denoised.wav"]
        
        for file in output_files:
            file_path = self.root_dir / file
            if file_path.exists():
                size = file_path.stat().st_size
                print(f"  {file} ({size} 字节)")
        
        print("\n🎵 下一步:")
        print("1. 使用音频播放器试听 output_denoised.wav")
        print("2. 与原始音频 3.wav 进行对比")
        print("3. 评估您的自定义模型的降噪效果")
        
        return True

def main():
    tester = CModelTester()
    success = tester.run_full_test()
    
    if success:
        print("\n🎊 恭喜！您的RNNoise C语言模型测试成功！")
    else:
        print("\n😞 测试过程中遇到了问题")
        print("请根据上述错误信息进行修复")

if __name__ == "__main__":
    main()
