#!/usr/bin/env python3
"""
RNNoise C语言模型完整测试脚本
自动化执行编译、转换、测试、分析的完整流程
"""

import os
import sys
import subprocess
import glob
import time
import platform
from pathlib import Path

class RNNoiseCTester:
    """RNNoise C语言测试器"""
    
    def __init__(self):
        self.system = platform.system().lower()
        self.is_windows = self.system == 'windows'
        
        # 设置可执行文件扩展名
        self.exe_ext = '.exe' if self.is_windows else ''
        
        print(f"检测到操作系统: {platform.system()}")
    
    def run_command(self, cmd, cwd=None, shell=None):
        """执行命令"""
        if shell is None:
            shell = self.is_windows
        
        try:
            result = subprocess.run(cmd, cwd=cwd, shell=shell, 
                                  capture_output=True, text=True, check=True)
            return True, result.stdout, result.stderr
        except subprocess.CalledProcessError as e:
            return False, e.stdout, e.stderr
    
    def step_1_compile(self):
        """步骤1: 编译程序"""
        print("\n" + "="*50)
        print("步骤1: 编译RNNoise程序")
        print("="*50)
        
        if self.is_windows:
            build_script = "build_test.bat"
        else:
            build_script = "build_test.sh"
            # 设置执行权限
            os.chmod(build_script, 0o755)
        
        if not os.path.exists(build_script):
            print(f"错误: 编译脚本不存在: {build_script}")
            return False
        
        print(f"执行编译脚本: {build_script}")
        
        if self.is_windows:
            success, stdout, stderr = self.run_command([build_script])
        else:
            success, stdout, stderr = self.run_command([f"./{build_script}"])
        
        if success:
            print("✓ 编译成功")
            return True
        else:
            print("✗ 编译失败")
            print("STDOUT:", stdout)
            print("STDERR:", stderr)
            return False
    
    def step_2_prepare_data(self):
        """步骤2: 准备测试数据"""
        print("\n" + "="*50)
        print("步骤2: 准备测试数据")
        print("="*50)
        
        # 检查是否有WAV文件
        wav_files = glob.glob("noise_voice/*.wav")
        if not wav_files:
            print("警告: 未找到noise_voice目录中的WAV文件")
            return False
        
        print(f"找到 {len(wav_files)} 个WAV文件")
        
        # 转换WAV为PCM
        cmd = [sys.executable, "convert_audio.py", "--prepare-test", "--max-files", "5"]
        success, stdout, stderr = self.run_command(cmd)
        
        if success:
            print("✓ 测试数据准备成功")
            return True
        else:
            print("✗ 测试数据准备失败")
            print("STDERR:", stderr)
            return False
    
    def step_3_run_tests(self):
        """步骤3: 运行测试"""
        print("\n" + "="*50)
        print("步骤3: 运行降噪测试")
        print("="*50)
        
        # 查找PCM输入文件
        pcm_files = glob.glob("test_pcm_input/*.pcm")
        if not pcm_files:
            print("错误: 未找到PCM输入文件")
            return False
        
        print(f"找到 {len(pcm_files)} 个PCM测试文件")
        
        # 测试程序列表
        test_programs = []
        if os.path.exists(f"test_custom_model{self.exe_ext}"):
            test_programs.append(("自定义模型", f"test_custom_model{self.exe_ext}"))
        if os.path.exists(f"rnnoise_demo{self.exe_ext}"):
            test_programs.append(("原始模型", f"rnnoise_demo{self.exe_ext}"))
        
        if not test_programs:
            print("错误: 未找到测试程序")
            return False
        
        results = []
        
        for program_name, program_path in test_programs:
            print(f"\n--- 测试 {program_name} ---")
            
            for i, pcm_file in enumerate(pcm_files[:3]):  # 测试前3个文件
                filename = os.path.basename(pcm_file)
                name_without_ext = os.path.splitext(filename)[0]
                output_file = f"output_{program_name.replace('模型', '')}_{name_without_ext}.pcm"
                
                print(f"处理文件 {i+1}/{min(3, len(pcm_files))}: {filename}")
                
                # 运行测试
                cmd = [f"./{program_path}", pcm_file, output_file]
                start_time = time.time()
                success, stdout, stderr = self.run_command(cmd)
                end_time = time.time()
                
                if success:
                    processing_time = end_time - start_time
                    file_size = os.path.getsize(output_file) if os.path.exists(output_file) else 0
                    
                    print(f"  ✓ 处理成功，耗时: {processing_time:.2f}秒")
                    
                    results.append({
                        'program': program_name,
                        'input_file': filename,
                        'output_file': output_file,
                        'processing_time': processing_time,
                        'output_size': file_size,
                        'success': True
                    })
                else:
                    print(f"  ✗ 处理失败")
                    print(f"  错误信息: {stderr}")
                    
                    results.append({
                        'program': program_name,
                        'input_file': filename,
                        'success': False,
                        'error': stderr
                    })
        
        self.test_results = results
        return len([r for r in results if r['success']]) > 0
    
    def step_4_convert_results(self):
        """步骤4: 转换结果为WAV"""
        print("\n" + "="*50)
        print("步骤4: 转换结果为WAV格式")
        print("="*50)
        
        cmd = [sys.executable, "convert_audio.py", "--convert-results"]
        success, stdout, stderr = self.run_command(cmd)
        
        if success:
            print("✓ 结果转换成功")
            return True
        else:
            print("✗ 结果转换失败")
            print("STDERR:", stderr)
            return False
    
    def step_5_analyze_results(self):
        """步骤5: 分析测试结果"""
        print("\n" + "="*50)
        print("步骤5: 测试结果分析")
        print("="*50)
        
        if not hasattr(self, 'test_results'):
            print("没有测试结果可分析")
            return
        
        successful_tests = [r for r in self.test_results if r['success']]
        failed_tests = [r for r in self.test_results if not r['success']]
        
        print(f"测试总数: {len(self.test_results)}")
        print(f"成功: {len(successful_tests)}")
        print(f"失败: {len(failed_tests)}")
        
        if successful_tests:
            print("\n成功的测试:")
            for result in successful_tests:
                print(f"  {result['program']}: {result['input_file']} -> {result['output_file']}")
                print(f"    处理时间: {result['processing_time']:.2f}秒")
                print(f"    输出大小: {result['output_size']} 字节")
        
        if failed_tests:
            print("\n失败的测试:")
            for result in failed_tests:
                print(f"  {result['program']}: {result['input_file']}")
                print(f"    错误: {result.get('error', '未知错误')}")
        
        # 检查输出文件
        wav_files = glob.glob("test_results_wav/*.wav")
        if wav_files:
            print(f"\n生成的WAV文件 ({len(wav_files)} 个):")
            for wav_file in wav_files:
                print(f"  {wav_file}")
    
    def run_full_test(self):
        """运行完整测试流程"""
        print("RNNoise C语言模型完整测试")
        print("="*50)
        
        steps = [
            ("编译程序", self.step_1_compile),
            ("准备数据", self.step_2_prepare_data),
            ("运行测试", self.step_3_run_tests),
            ("转换结果", self.step_4_convert_results),
            ("分析结果", self.step_5_analyze_results),
        ]
        
        for step_name, step_func in steps:
            try:
                success = step_func()
                if not success and step_name in ["编译程序", "准备数据", "运行测试"]:
                    print(f"\n✗ {step_name}失败，测试中止")
                    return False
            except Exception as e:
                print(f"\n✗ {step_name}出现异常: {e}")
                if step_name in ["编译程序", "准备数据", "运行测试"]:
                    return False
        
        print("\n" + "="*50)
        print("测试完成！")
        print("="*50)
        
        print("\n下一步建议:")
        print("1. 查看 test_results_wav/ 目录中的WAV文件")
        print("2. 使用音频播放器比较原始音频和降噪后的音频")
        print("3. 根据效果调整训练参数重新训练模型")
        
        return True

def main():
    if len(sys.argv) > 1 and sys.argv[1] == "--help":
        print("RNNoise C语言模型测试脚本")
        print("用法: python run_c_test.py")
        print("该脚本将自动执行完整的测试流程")
        return
    
    tester = RNNoiseCTester()
    success = tester.run_full_test()
    
    if success:
        print("\n🎉 测试流程执行成功！")
    else:
        print("\n❌ 测试流程执行失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()
