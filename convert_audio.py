#!/usr/bin/env python3
"""
音频格式转换工具
用于RNNoise测试的音频格式转换
支持WAV <-> PCM 互转
"""

import os
import sys
import subprocess
import argparse
import glob
from pathlib import Path

class AudioConverter:
    """音频格式转换器"""
    
    def __init__(self, ffmpeg_path=None):
        """初始化转换器"""
        if ffmpeg_path:
            self.ffmpeg_path = ffmpeg_path
        else:
            # 尝试找到ffmpeg
            possible_paths = [
                "env/ffmpeg.exe",
                "ffmpeg.exe", 
                "ffmpeg",
                "/usr/bin/ffmpeg",
                "/usr/local/bin/ffmpeg"
            ]
            
            self.ffmpeg_path = None
            for path in possible_paths:
                if os.path.exists(path) or self.check_command(path):
                    self.ffmpeg_path = path
                    break
            
            if not self.ffmpeg_path:
                print("错误: 未找到ffmpeg程序")
                print("请安装ffmpeg或指定ffmpeg路径")
                sys.exit(1)
        
        print(f"使用ffmpeg: {self.ffmpeg_path}")
    
    def check_command(self, cmd):
        """检查命令是否存在"""
        try:
            subprocess.run([cmd, "-version"], 
                         capture_output=True, check=True)
            return True
        except:
            return False
    
    def wav_to_pcm(self, wav_file, pcm_file, sample_rate=48000):
        """WAV转PCM"""
        cmd = [
            self.ffmpeg_path,
            '-i', wav_file,
            '-f', 's16le',  # 16位小端格式
            '-ar', str(sample_rate),  # 采样率
            '-ac', '1',  # 单声道
            '-y',  # 覆盖输出文件
            pcm_file
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            print(f"✓ 转换成功: {wav_file} -> {pcm_file}")
            return True
        except subprocess.CalledProcessError as e:
            print(f"✗ 转换失败 {wav_file}: {e.stderr}")
            return False
    
    def pcm_to_wav(self, pcm_file, wav_file, sample_rate=48000):
        """PCM转WAV"""
        cmd = [
            self.ffmpeg_path,
            '-f', 's16le',  # 输入格式：16位小端
            '-ar', str(sample_rate),  # 采样率
            '-ac', '1',  # 单声道
            '-i', pcm_file,
            '-y',  # 覆盖输出文件
            wav_file
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            print(f"✓ 转换成功: {pcm_file} -> {wav_file}")
            return True
        except subprocess.CalledProcessError as e:
            print(f"✗ 转换失败 {pcm_file}: {e.stderr}")
            return False
    
    def convert_directory(self, input_dir, output_dir, input_ext, output_ext, 
                         sample_rate=48000, max_files=None):
        """批量转换目录中的文件"""
        if not os.path.exists(input_dir):
            print(f"错误: 输入目录不存在: {input_dir}")
            return []
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 查找输入文件
        pattern = os.path.join(input_dir, f"*.{input_ext}")
        input_files = glob.glob(pattern)
        
        if max_files:
            input_files = input_files[:max_files]
        
        print(f"找到 {len(input_files)} 个 {input_ext} 文件")
        
        converted_files = []
        for input_file in input_files:
            filename = os.path.basename(input_file)
            name_without_ext = os.path.splitext(filename)[0]
            output_file = os.path.join(output_dir, f"{name_without_ext}.{output_ext}")
            
            if input_ext.lower() == 'wav' and output_ext.lower() == 'pcm':
                success = self.wav_to_pcm(input_file, output_file, sample_rate)
            elif input_ext.lower() == 'pcm' and output_ext.lower() == 'wav':
                success = self.pcm_to_wav(input_file, output_file, sample_rate)
            else:
                print(f"不支持的转换: {input_ext} -> {output_ext}")
                continue
            
            if success:
                converted_files.append(output_file)
        
        print(f"成功转换 {len(converted_files)} 个文件")
        return converted_files

def main():
    parser = argparse.ArgumentParser(description='RNNoise音频格式转换工具')
    parser.add_argument('--input-dir', required=True, help='输入目录')
    parser.add_argument('--output-dir', required=True, help='输出目录')
    parser.add_argument('--input-format', choices=['wav', 'pcm'], required=True, 
                       help='输入格式')
    parser.add_argument('--output-format', choices=['wav', 'pcm'], required=True,
                       help='输出格式')
    parser.add_argument('--sample-rate', type=int, default=48000,
                       help='采样率 (默认: 48000)')
    parser.add_argument('--max-files', type=int, help='最大转换文件数')
    parser.add_argument('--ffmpeg-path', help='ffmpeg程序路径')
    
    # 预设转换模式
    parser.add_argument('--prepare-test', action='store_true',
                       help='准备测试数据 (WAV -> PCM)')
    parser.add_argument('--convert-results', action='store_true', 
                       help='转换测试结果 (PCM -> WAV)')
    
    args = parser.parse_args()
    
    # 预设模式
    if args.prepare_test:
        print("=== 准备测试数据模式 ===")
        converter = AudioConverter(args.ffmpeg_path)
        
        # 转换噪声音频
        print("\n转换噪声音频文件...")
        converter.convert_directory(
            'noise_voice', 'test_pcm_input', 'wav', 'pcm',
            args.sample_rate, args.max_files)
        
        # 转换清晰音频（如果存在）
        if os.path.exists('clean_voice'):
            print("\n转换清晰音频文件...")
            converter.convert_directory(
                'clean_voice', 'test_pcm_clean', 'wav', 'pcm',
                args.sample_rate, args.max_files)
        
        print("\n✓ 测试数据准备完成！")
        print("现在可以使用以下命令测试:")
        print("./test_custom_model test_pcm_input/xxx.pcm output_xxx.pcm")
        return
    
    if args.convert_results:
        print("=== 转换测试结果模式 ===")
        converter = AudioConverter(args.ffmpeg_path)
        
        # 查找PCM输出文件
        pcm_files = glob.glob("output_*.pcm") + glob.glob("*_denoised.pcm")
        
        if not pcm_files:
            print("未找到PCM输出文件")
            return
        
        os.makedirs('test_results_wav', exist_ok=True)
        
        for pcm_file in pcm_files:
            filename = os.path.basename(pcm_file)
            name_without_ext = os.path.splitext(filename)[0]
            wav_file = os.path.join('test_results_wav', f"{name_without_ext}.wav")
            converter.pcm_to_wav(pcm_file, wav_file, args.sample_rate)
        
        print(f"\n✓ 转换完成！WAV文件保存在 test_results_wav/ 目录")
        return
    
    # 常规转换模式
    converter = AudioConverter(args.ffmpeg_path)
    converter.convert_directory(
        args.input_dir, args.output_dir, 
        args.input_format, args.output_format,
        args.sample_rate, args.max_files)

if __name__ == "__main__":
    main()
