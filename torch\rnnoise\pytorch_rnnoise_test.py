#!/usr/bin/env python3
"""
正确的PyTorch RNNoise模型测试 - 完全基于C语言实现
"""

import os
import sys
import subprocess
import numpy as np
import torch
import torch.nn.functional as F
from pathlib import Path
import scipy.signal
from scipy.fftpack import dct

# 添加当前目录到Python路径
sys.path.append('.')
sys.path.append('..')
sys.path.append('../../RNNoise_train')

# RNNoise常量定义 (与C语言完全一致)
FRAME_SIZE = 480
WINDOW_SIZE = 2 * FRAME_SIZE  # 960
FREQ_SIZE = FRAME_SIZE + 1    # 481
NB_BANDS = 32
NB_FEATURES = 2 * NB_BANDS + 1  # 65

def run_command(cmd, shell=True):
    """运行命令"""
    try:
        result = subprocess.run(cmd, shell=shell, capture_output=True, text=True, check=True)
        return True, result.stdout, result.stderr
    except subprocess.CalledProcessError as e:
        return False, e.stdout, e.stderr

def load_audio_with_ffmpeg(wav_file, ffmpeg_path):
    """使用ffmpeg加载音频"""
    print(f"使用ffmpeg加载音频: {wav_file}")
    
    # 转换为PCM
    pcm_file = "temp_audio.pcm"
    cmd = [ffmpeg_path, "-i", wav_file, "-f", "s16le", "-ar", "48000", "-ac", "1", "-y", pcm_file]
    
    success, stdout, stderr = run_command(cmd)
    if not success:
        print(f"音频转换失败: {stderr}")
        return None
    
    # 读取PCM数据
    try:
        audio_data = np.fromfile(pcm_file, dtype=np.int16)
        print(f"音频加载成功: {len(audio_data)} 样本 ({len(audio_data)/48000:.2f}秒)")
        
        # 转换为浮点数
        audio_float = audio_data.astype(np.float32)
        
        # 清理临时文件
        if Path(pcm_file).exists():
            os.remove(pcm_file)
        
        return audio_float
    except Exception as e:
        print(f"读取PCM数据失败: {e}")
        return None

def save_audio_with_ffmpeg(audio_data, output_file, ffmpeg_path):
    """使用ffmpeg保存音频"""
    print(f"保存音频: {output_file}")
    
    # 转换为int16
    audio_int16 = np.clip(audio_data, -32768, 32767).astype(np.int16)
    
    # 保存为PCM
    pcm_file = "temp_output.pcm"
    audio_int16.tofile(pcm_file)
    
    # 转换为WAV
    cmd = [ffmpeg_path, "-f", "s16le", "-ar", "48000", "-ac", "1", "-i", pcm_file, "-y", output_file]
    
    success, stdout, stderr = run_command(cmd)
    
    # 清理临时文件
    if Path(pcm_file).exists():
        os.remove(pcm_file)
    
    if success:
        print(f"音频保存成功: {output_file}")
        return True
    else:
        print(f"音频保存失败: {stderr}")
        return False

def load_rnnoise_model():
    """加载RNNoise模型"""
    print("\n=== 加载RNNoise模型 ===")

    # 查找模型文件
    model_patterns = [
        "*.pth",
        "../*.pth",
        "../../*.pth",
        "output/checkpoints/*.pth",
        "../output/checkpoints/*.pth",
        "../../output/checkpoints/*.pth"
    ]

    model_files = []
    for pattern in model_patterns:
        model_files.extend(list(Path(".").glob(pattern)))

    if not model_files:
        print("✗ 未找到PyTorch模型文件(.pth)")
        return None

    # 选择最新的模型文件
    latest_model = max(model_files, key=lambda x: x.stat().st_mtime)
    print(f"找到模型文件: {latest_model}")

    try:
        # 加载模型文件
        checkpoint = torch.load(latest_model, map_location='cpu')
        print(f"✓ 模型文件加载成功")

        # 尝试多种方式导入RNNoise模型
        RNNoise = None
        import_paths = [
            "rnnoise",
            "../rnnoise",
            "../../RNNoise_train/rnnoise",
            "RNNoise_train.rnnoise"
        ]

        for import_path in import_paths:
            try:
                if "." in import_path:
                    module_parts = import_path.split(".")
                    module = __import__(import_path, fromlist=[module_parts[-1]])
                    RNNoise = getattr(module, 'RNNoise')
                else:
                    sys.path.append(import_path)
                    import rnnoise
                    RNNoise = rnnoise.RNNoise
                print(f"✓ 成功导入RNNoise模块: {import_path}")
                break
            except (ImportError, AttributeError, FileNotFoundError):
                continue

        if RNNoise is None:
            print("⚠ 无法导入RNNoise模块，尝试直接加载权重")
            # 直接返回checkpoint，让调用者处理
            return checkpoint

        # 检查模型结构
        if 'model_kwargs' in checkpoint:
            model_kwargs = checkpoint['model_kwargs']
            print(f"模型参数: {model_kwargs}")

            # 创建模型
            model = RNNoise(**model_kwargs)

            if 'state_dict' in checkpoint:
                model.load_state_dict(checkpoint['state_dict'])
                print(f"✓ 权重加载成功，参数数量: {len(checkpoint['state_dict'])}")
            else:
                print("⚠ 未找到state_dict，检查模型文件格式")

            model.eval()
            return model
        else:
            print("⚠ 模型文件格式不标准，尝试直接加载")
            # 如果没有model_kwargs，尝试使用默认参数
            try:
                model = RNNoise()  # 使用默认参数
                if hasattr(checkpoint, 'keys') and len(checkpoint) > 0:
                    # 如果checkpoint直接是state_dict
                    model.load_state_dict(checkpoint)
                    print(f"✓ 直接加载权重成功")
                model.eval()
                return model
            except Exception as e:
                print(f"✗ 直接加载失败: {e}")
                return checkpoint

    except Exception as e:
        print(f"✗ 模型加载失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def apply_window(x):
    """应用汉宁窗 (与C语言实现一致)"""
    n = len(x)
    for i in range(n):
        x[i] *= 0.5 - 0.5 * np.cos(2 * np.pi * i / (n - 1))

def compute_band_energy(Ex, X):
    """计算频带能量 (与C语言实现一致)"""
    # eband20ms数组定义 (来自C代码)
    eband20ms = [0, 1, 2, 3, 4, 5, 6, 7, 8, 10, 12, 14, 16, 20, 24, 28, 34, 40, 48, 60, 78, 100,
                 120, 156, 200, 260, 340, 440, 540, 640, 760, 880, 1000]

    for i in range(NB_BANDS):
        sum_energy = 0.0
        for j in range(eband20ms[i], eband20ms[i+1]):
            if j < len(X):
                sum_energy += X[j].real**2 + X[j].imag**2
        Ex[i] = sum_energy

def extract_rnnoise_features(audio_data):
    """提取RNNoise标准的65维特征 (完全基于C语言实现)"""
    print("提取RNNoise标准特征...")

    # 归一化音频数据
    audio_data = audio_data.astype(np.float32) / 32768.0

    num_frames = len(audio_data) // FRAME_SIZE
    features_list = []

    # 状态变量 (模拟C语言中的DenoiseState)
    analysis_mem = np.zeros(FRAME_SIZE, dtype=np.float32)
    pitch_buf = np.zeros(1024, dtype=np.float32)  # PITCH_BUF_SIZE
    last_period = 100
    last_gain = 0.0
    mem_hp_x = np.zeros(2, dtype=np.float32)

    print(f"处理 {num_frames} 帧...")

    for frame_idx in range(num_frames):
        start_idx = frame_idx * FRAME_SIZE
        end_idx = start_idx + FRAME_SIZE

        if end_idx > len(audio_data):
            # 最后一帧用零填充
            frame = np.zeros(FRAME_SIZE, dtype=np.float32)
            frame[:len(audio_data) - start_idx] = audio_data[start_idx:]
        else:
            frame = audio_data[start_idx:end_idx]

        # 高通滤波 (与C语言实现一致)
        a_hp = np.array([-1.99599, 0.99600])
        b_hp = np.array([-2.0, 1.0])
        x = np.zeros(FRAME_SIZE, dtype=np.float32)

        # 双二阶滤波器
        for i in range(FRAME_SIZE):
            xi = frame[i]
            yi = xi + mem_hp_x[0]
            mem_hp_x[0] = mem_hp_x[1] + (b_hp[0] * xi - a_hp[0] * yi)
            mem_hp_x[1] = (b_hp[1] * xi - a_hp[1] * yi)
            x[i] = yi

        # 帧分析 - 计算FFT
        tmp = np.concatenate([analysis_mem, x])
        apply_window(tmp)

        # FFT (960点)
        X = np.fft.fft(tmp, n=WINDOW_SIZE)
        X = X[:FREQ_SIZE]  # 只取前481个点

        # 计算频带能量
        Ex = np.zeros(NB_BANDS, dtype=np.float32)
        compute_band_energy(Ex, X)

        # 基音分析 (简化版)
        pitch_buf = np.roll(pitch_buf, -FRAME_SIZE)
        pitch_buf[-FRAME_SIZE:] = x

        # 基音检测 (简化)
        pitch_frame = pitch_buf[-960:]  # PITCH_FRAME_SIZE
        autocorr = np.correlate(pitch_frame, pitch_frame, mode='full')
        autocorr = autocorr[len(autocorr)//2:]

        # 寻找基音周期
        min_period = 60   # PITCH_MIN_PERIOD
        max_period = 768  # PITCH_MAX_PERIOD

        if len(autocorr) > max_period:
            pitch_corr = autocorr[min_period:max_period]
            if len(pitch_corr) > 0:
                pitch_index = np.argmax(pitch_corr) + min_period
                gain = pitch_corr[np.argmax(pitch_corr)] / (autocorr[0] + 1e-10)
            else:
                pitch_index = last_period
                gain = last_gain
        else:
            pitch_index = last_period
            gain = last_gain

        # 基音滤波
        p = np.zeros(WINDOW_SIZE, dtype=np.float32)
        if pitch_index < len(pitch_buf):
            p_start = len(pitch_buf) - WINDOW_SIZE - pitch_index
            if p_start >= 0:
                p = pitch_buf[p_start:p_start + WINDOW_SIZE].copy()

        apply_window(p)
        P = np.fft.fft(p, n=WINDOW_SIZE)
        P = P[:FREQ_SIZE]

        # 计算基音频带能量
        Ep = np.zeros(NB_BANDS, dtype=np.float32)
        compute_band_energy(Ep, P)

        # 计算互相关
        Exp = np.zeros(NB_BANDS, dtype=np.float32)
        eband20ms = [0, 1, 2, 3, 4, 5, 6, 7, 8, 10, 12, 14, 16, 20, 24, 28, 34, 40, 48, 60, 78, 100,
                     120, 156, 200, 260, 340, 440, 540, 640, 760, 880, 1000]

        for i in range(NB_BANDS):
            corr_sum = 0.0
            for j in range(eband20ms[i], min(eband20ms[i+1], len(X), len(P))):
                corr_sum += (X[j].real * P[j].real + X[j].imag * P[j].imag)
            Exp[i] = corr_sum / np.sqrt(0.001 + Ex[i] * Ep[i])

        # 构建65维特征向量
        features = np.zeros(NB_FEATURES, dtype=np.float32)

        # 1. Bark频谱的DCT (22维)
        Ly = np.zeros(NB_BANDS, dtype=np.float32)
        logMax = -2.0
        follow = -2.0
        E = 0.0

        for i in range(NB_BANDS):
            Ly[i] = np.log10(1e-2 + Ex[i])
            Ly[i] = max(logMax - 7, max(follow - 1.5, Ly[i]))
            logMax = max(logMax, Ly[i])
            follow = max(follow - 1.5, Ly[i])
            E += Ex[i]

        # DCT变换
        dct_coeffs = dct(Ly, type=2, norm='ortho')
        features[:NB_BANDS] = dct_coeffs
        features[0] -= 12  # C代码中的调整
        features[1] -= 4   # C代码中的调整

        # 2. 基音相关特征的DCT (22维)
        dct_exp = dct(Exp, type=2, norm='ortho')
        features[NB_BANDS:2*NB_BANDS] = dct_exp

        # 3. 基音特征 (1维)
        features[2*NB_BANDS] = 0.01 * (pitch_index - 300)

        # 检查静音
        if E < 0.04:
            features.fill(0.0)  # 静音帧清零

        features_list.append(features.copy())

        # 更新状态
        analysis_mem = x.copy()
        last_period = pitch_index
        last_gain = gain

        if frame_idx % 100 == 0:
            print(f"处理进度: {frame_idx+1}/{num_frames} 帧")

    if features_list:
        features_array = np.array(features_list, dtype=np.float32)
        print(f"特征提取完成: {features_array.shape}")
        return features_array
    else:
        print("特征提取失败")
        return None



def process_with_rnnoise_model(model, features):
    """使用RNNoise模型处理"""
    print("使用RNNoise模型处理...")

    # 转换为PyTorch张量
    features_tensor = torch.FloatTensor(features).unsqueeze(0)  # 添加batch维度

    with torch.no_grad():
        try:
            # 运行模型
            output = model(features_tensor)

            if isinstance(output, tuple) and len(output) >= 2:
                # 模型输出 (gain, vad, states)
                pred_gain = output[0]
                pred_vad = output[1]

                # 获取增益和VAD
                gain = pred_gain.squeeze().numpy()
                vad = pred_vad.squeeze().numpy()

                print(f"模型处理完成: gain shape={gain.shape}, vad shape={vad.shape}")
                print(f"平均增益: {np.mean(gain):.3f}, 平均VAD: {np.mean(vad):.3f}")
                print(f"增益范围: [{np.min(gain):.3f}, {np.max(gain):.3f}]")

                return gain, vad
            else:
                print(f"模型输出格式不符合预期: {type(output)}")
                return None, None

        except Exception as e:
            print(f"模型处理失败: {e}")
            import traceback
            traceback.print_exc()
            return None, None

def apply_rnnoise_gain(audio_data, gain):
    """应用RNNoise增益 (基于C语言实现)"""
    print("应用RNNoise增益...")

    # 归一化音频数据
    audio_data = audio_data.astype(np.float32) / 32768.0

    num_frames = min(len(gain), len(audio_data) // FRAME_SIZE)
    processed_audio = np.zeros_like(audio_data)

    # 状态变量
    synthesis_mem = np.zeros(FRAME_SIZE, dtype=np.float32)

    print(f"处理 {num_frames} 帧，增益形状: {gain.shape}")

    for i in range(num_frames):
        start_idx = i * FRAME_SIZE
        end_idx = start_idx + FRAME_SIZE

        if end_idx > len(audio_data):
            frame = np.zeros(FRAME_SIZE, dtype=np.float32)
            frame[:len(audio_data) - start_idx] = audio_data[start_idx:]
        else:
            frame = audio_data[start_idx:end_idx]

        # 帧分析
        tmp = np.concatenate([synthesis_mem, frame])
        apply_window(tmp)

        # FFT
        Y = np.fft.fft(tmp, n=WINDOW_SIZE)
        Y = Y[:FREQ_SIZE]  # 前481个点

        # 应用增益到频带
        if len(gain.shape) == 2:  # 32维增益
            frame_gain = gain[i]

            # eband20ms数组 (频带边界)
            eband20ms = [0, 1, 2, 3, 4, 5, 6, 7, 8, 10, 12, 14, 16, 20, 24, 28, 34, 40, 48, 60, 78, 100,
                         120, 156, 200, 260, 340, 440, 540, 640, 760, 880, 1000]

            # 按频带应用增益
            for band in range(min(NB_BANDS, len(frame_gain))):
                band_gain = np.clip(frame_gain[band], 0.0, 1.0)
                start_bin = eband20ms[band]
                end_bin = min(eband20ms[band + 1], len(Y))

                for j in range(start_bin, end_bin):
                    Y[j] *= band_gain
        else:
            # 1维增益，应用到所有频率
            frame_gain = np.clip(gain[i], 0.0, 1.0)
            Y *= frame_gain

        # 扩展到完整的FFT (对称性)
        Y_full = np.zeros(WINDOW_SIZE, dtype=complex)
        Y_full[:FREQ_SIZE] = Y
        # 负频率部分 (共轭对称)
        Y_full[FREQ_SIZE:] = np.conj(Y[FREQ_SIZE-2:0:-1])

        # IFFT
        y = np.real(np.fft.ifft(Y_full))

        # 应用窗函数
        apply_window(y)

        # 重叠相加
        output_frame = y[:FRAME_SIZE] + synthesis_mem

        if end_idx > len(audio_data):
            processed_audio[start_idx:] = output_frame[:len(audio_data) - start_idx]
        else:
            processed_audio[start_idx:end_idx] = output_frame

        # 更新合成内存
        synthesis_mem = y[FRAME_SIZE:].copy()

    # 转换回int16
    processed_audio = np.clip(processed_audio * 32768.0, -32768, 32767).astype(np.int16)

    print(f"RNNoise增益应用完成，处理了 {num_frames} 帧")
    return processed_audio

def analyze_audio_quality(original, processed):
    """分析音频质量"""
    print("\n=== 音频质量分析 ===")
    
    # 计算RMS能量
    original_rms = np.sqrt(np.mean(original**2))
    processed_rms = np.sqrt(np.mean(processed**2))
    
    # 计算降噪效果
    if processed_rms > 0:
        noise_reduction = 20 * np.log10(original_rms / processed_rms)
    else:
        noise_reduction = float('inf')
    
    # 计算静音帧比例
    silence_threshold = 100
    original_silence = np.sum(np.abs(original) < silence_threshold) / len(original)
    processed_silence = np.sum(np.abs(processed) < silence_threshold) / len(processed)
    
    print(f"原始音频RMS: {original_rms:.2f}")
    print(f"处理音频RMS: {processed_rms:.2f}")
    print(f"降噪效果: {noise_reduction:.2f} dB")
    print(f"原始静音比例: {original_silence:.2%}")
    print(f"处理静音比例: {processed_silence:.2%}")

def main():
    print("🎯 PyTorch RNNoise模型测试")
    print("="*50)
    
    # 检查文件
    print("\n=== 检查文件 ===")
    #########################################################################################################
    test_audio = Path("20250716_084323.wav")
    ffmpeg_path = Path("../../env/ffmpeg.exe")
    
    if not test_audio.exists():
        print(f"✗ 测试音频不存在: {test_audio}")
        return False
    
    if not ffmpeg_path.exists():
        print(f"✗ FFmpeg不存在: {ffmpeg_path}")
        return False
    
    print(f"✓ 测试音频: {test_audio} ({test_audio.stat().st_size:,} 字节)")
    print(f"✓ FFmpeg: {ffmpeg_path}")
    
    # 加载音频
    print("\n=== 加载音频 ===")
    audio_data = load_audio_with_ffmpeg(str(test_audio), str(ffmpeg_path))
    if audio_data is None:
        return False
    
    # 加载RNNoise模型
    model = load_rnnoise_model()
    
    if model is not None:
        print("\n=== 使用RNNoise模型处理 ===")
        
        # 提取RNNoise标准特征
        features = extract_rnnoise_features(audio_data)
        
        # 使用模型处理
        gain, vad = process_with_rnnoise_model(model, features)
        
        if gain is not None:
            # 应用RNNoise增益
            processed_audio = apply_rnnoise_gain(audio_data, gain)
            output_file = "output_pytorch_rnnoise.wav"
        else:
            print("⚠ 模型处理失败，使用简单算法")
            processed_audio = audio_data * 0.8  # 简单衰减
            output_file = "output_simple_processed.wav"
    else:
        print("\n=== 使用简单处理算法 ===")
        processed_audio = audio_data * 0.8  # 简单衰减
        output_file = "output_simple_processed.wav"
    
    # 分析质量
    analyze_audio_quality(audio_data, processed_audio)
    
    # 保存结果
    print(f"\n=== 保存结果 ===")
    success = save_audio_with_ffmpeg(processed_audio, output_file, str(ffmpeg_path))
    
    if not success:
        return False
    
    # 生成报告
    print("\n" + "="*50)
    print("🎉 PyTorch RNNoise测试完成！")
    print("="*50)
    
    print(f"\n📁 生成的文件:")
    print(f"  原始音频: {test_audio}")
    print(f"  处理音频: {output_file}")
    
    if Path(output_file).exists():
        output_size = Path(output_file).stat().st_size
        print(f"  输出大小: {output_size:,} 字节")
    
    print(f"\n🎵 测试结果:")
    if model is not None:
        print("1. ✅ 成功使用了PyTorch RNNoise模型")
        print("2. 🎯 这是基于您训练模型的真实降噪效果")
    else:
        print("1. ⚠ 未能加载RNNoise模型，使用了简单处理")
        print("2. 💡 请确保rnnoise.py文件存在且模型文件完整")
    
    print("3. 🎧 请使用音频播放器比较两个文件的效果")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎊 PyTorch RNNoise测试成功完成！")
        else:
            print("\n❌ PyTorch RNNoise测试失败")
            sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
