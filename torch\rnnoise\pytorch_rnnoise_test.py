#!/usr/bin/env python3
"""
使用真正的PyTorch RNNoise模型进行测试
"""

import os
import sys
import subprocess
import numpy as np
import torch
import torch.nn.functional as F
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append('.')
sys.path.append('..')
sys.path.append('../../RNNoise_train')

def run_command(cmd, shell=True):
    """运行命令"""
    try:
        result = subprocess.run(cmd, shell=shell, capture_output=True, text=True, check=True)
        return True, result.stdout, result.stderr
    except subprocess.CalledProcessError as e:
        return False, e.stdout, e.stderr

def load_audio_with_ffmpeg(wav_file, ffmpeg_path):
    """使用ffmpeg加载音频"""
    print(f"使用ffmpeg加载音频: {wav_file}")
    
    # 转换为PCM
    pcm_file = "temp_audio.pcm"
    cmd = [ffmpeg_path, "-i", wav_file, "-f", "s16le", "-ar", "48000", "-ac", "1", "-y", pcm_file]
    
    success, stdout, stderr = run_command(cmd)
    if not success:
        print(f"音频转换失败: {stderr}")
        return None
    
    # 读取PCM数据
    try:
        audio_data = np.fromfile(pcm_file, dtype=np.int16)
        print(f"音频加载成功: {len(audio_data)} 样本 ({len(audio_data)/48000:.2f}秒)")
        
        # 转换为浮点数
        audio_float = audio_data.astype(np.float32)
        
        # 清理临时文件
        if Path(pcm_file).exists():
            os.remove(pcm_file)
        
        return audio_float
    except Exception as e:
        print(f"读取PCM数据失败: {e}")
        return None

def save_audio_with_ffmpeg(audio_data, output_file, ffmpeg_path):
    """使用ffmpeg保存音频"""
    print(f"保存音频: {output_file}")
    
    # 转换为int16
    audio_int16 = np.clip(audio_data, -32768, 32767).astype(np.int16)
    
    # 保存为PCM
    pcm_file = "temp_output.pcm"
    audio_int16.tofile(pcm_file)
    
    # 转换为WAV
    cmd = [ffmpeg_path, "-f", "s16le", "-ar", "48000", "-ac", "1", "-i", pcm_file, "-y", output_file]
    
    success, stdout, stderr = run_command(cmd)
    
    # 清理临时文件
    if Path(pcm_file).exists():
        os.remove(pcm_file)
    
    if success:
        print(f"音频保存成功: {output_file}")
        return True
    else:
        print(f"音频保存失败: {stderr}")
        return False

def load_rnnoise_model():
    """加载RNNoise模型"""
    print("\n=== 加载RNNoise模型 ===")

    # 查找模型文件
    model_patterns = [
        "*.pth",
        "../*.pth",
        "../../*.pth",
        "output/checkpoints/*.pth",
        "../output/checkpoints/*.pth",
        "../../output/checkpoints/*.pth"
    ]

    model_files = []
    for pattern in model_patterns:
        model_files.extend(list(Path(".").glob(pattern)))

    if not model_files:
        print("✗ 未找到PyTorch模型文件(.pth)")
        return None

    # 选择最新的模型文件
    latest_model = max(model_files, key=lambda x: x.stat().st_mtime)
    print(f"找到模型文件: {latest_model}")

    try:
        # 加载模型文件
        checkpoint = torch.load(latest_model, map_location='cpu')
        print(f"✓ 模型文件加载成功")

        # 尝试多种方式导入RNNoise模型
        RNNoise = None
        import_paths = [
            "rnnoise",
            "../rnnoise",
            "../../RNNoise_train/rnnoise",
            "RNNoise_train.rnnoise"
        ]

        for import_path in import_paths:
            try:
                if "." in import_path:
                    module_parts = import_path.split(".")
                    module = __import__(import_path, fromlist=[module_parts[-1]])
                    RNNoise = getattr(module, 'RNNoise')
                else:
                    sys.path.append(import_path)
                    import rnnoise
                    RNNoise = rnnoise.RNNoise
                print(f"✓ 成功导入RNNoise模块: {import_path}")
                break
            except (ImportError, AttributeError, FileNotFoundError):
                continue

        if RNNoise is None:
            print("⚠ 无法导入RNNoise模块，尝试直接加载权重")
            # 直接返回checkpoint，让调用者处理
            return checkpoint

        # 检查模型结构
        if 'model_kwargs' in checkpoint:
            model_kwargs = checkpoint['model_kwargs']
            print(f"模型参数: {model_kwargs}")

            # 创建模型
            model = RNNoise(**model_kwargs)

            if 'state_dict' in checkpoint:
                model.load_state_dict(checkpoint['state_dict'])
                print(f"✓ 权重加载成功，参数数量: {len(checkpoint['state_dict'])}")
            else:
                print("⚠ 未找到state_dict，检查模型文件格式")

            model.eval()
            return model
        else:
            print("⚠ 模型文件格式不标准，尝试直接加载")
            # 如果没有model_kwargs，尝试使用默认参数
            try:
                model = RNNoise()  # 使用默认参数
                if hasattr(checkpoint, 'keys') and len(checkpoint) > 0:
                    # 如果checkpoint直接是state_dict
                    model.load_state_dict(checkpoint)
                    print(f"✓ 直接加载权重成功")
                model.eval()
                return model
            except Exception as e:
                print(f"✗ 直接加载失败: {e}")
                return checkpoint

    except Exception as e:
        print(f"✗ 模型加载失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def extract_rnnoise_features(audio_data, frame_size=480):
    """提取RNNoise标准的65维输入特征"""
    print("提取RNNoise标准特征...")

    # 归一化音频数据
    audio_data = audio_data.astype(np.float32) / 32768.0

    num_frames = len(audio_data) // frame_size
    features = []

    for i in range(num_frames):
        start_idx = i * frame_size
        end_idx = start_idx + frame_size

        if end_idx > len(audio_data):
            # 最后一帧用零填充
            frame = np.zeros(frame_size, dtype=np.float32)
            frame[:len(audio_data) - start_idx] = audio_data[start_idx:]
        else:
            frame = audio_data[start_idx:end_idx]

        # 提取65维输入特征
        frame_features = extract_65d_input_features(frame)
        if frame_features is not None:
            features.append(frame_features)

    if features:
        features = np.array(features, dtype=np.float32)
        print(f"特征提取完成: {features.shape}")
        return features
    else:
        print("特征提取失败")
        return None

def extract_65d_input_features(frame):
    """提取单帧的65维输入特征（与训练时完全一致）"""
    try:
        frame_size = len(frame)

        # FFT变换
        fft_frame = np.fft.fft(frame, n=512)
        fft_magnitude = np.abs(fft_frame[:256])  # 只取前256个bin
        fft_power = fft_magnitude ** 2

        # 初始化65维输入特征
        input_features = np.zeros(65, dtype=np.float32)

        # 1. Bark频谱 (22维)
        bark_bands = 22
        for b in range(bark_bands):
            start_bin = int(b * 256 / bark_bands)
            end_bin = int((b + 1) * 256 / bark_bands)
            input_features[b] = np.mean(fft_magnitude[start_bin:end_bin])

        # 2. MFCC特征 (13维)
        mel_filters = 13
        for m in range(mel_filters):
            start_bin = int(m * 128 / mel_filters)
            end_bin = int((m + 1) * 128 / mel_filters)
            mel_energy = np.sum(fft_power[start_bin:end_bin])
            input_features[22 + m] = np.log(mel_energy + 1e-10)

        # 3. 基音特征 (6维)
        autocorr = np.correlate(frame, frame, mode='full')
        autocorr = autocorr[len(autocorr)//2:]

        min_period = 20
        max_period = 320

        if len(autocorr) > max_period:
            pitch_corr = autocorr[min_period:max_period]
            if len(pitch_corr) > 0:
                pitch_period = np.argmax(pitch_corr) + min_period
                pitch_gain = pitch_corr[np.argmax(pitch_corr)] / (autocorr[0] + 1e-10)

                input_features[35] = pitch_period / 320.0
                input_features[36] = np.clip(pitch_gain, 0, 1)
                input_features[37] = np.std(frame)
                input_features[38] = np.mean(np.abs(frame))
                input_features[39] = np.sum(frame ** 2) / frame_size
                input_features[40] = np.max(np.abs(frame))

        # 4. 频谱特征 (24维)
        freqs = np.arange(256) * 48000 / 512

        # 频谱质心
        spectral_centroid = np.sum(freqs * fft_magnitude) / (np.sum(fft_magnitude) + 1e-10)
        input_features[41] = spectral_centroid / 24000.0

        # 频谱带宽
        spectral_bandwidth = np.sqrt(np.sum(((freqs - spectral_centroid) ** 2) * fft_magnitude) / (np.sum(fft_magnitude) + 1e-10))
        input_features[42] = spectral_bandwidth / 12000.0

        # 频谱滚降
        cumsum_magnitude = np.cumsum(fft_magnitude)
        rolloff_threshold = 0.85 * cumsum_magnitude[-1]
        rolloff_idx = np.where(cumsum_magnitude >= rolloff_threshold)[0]
        if len(rolloff_idx) > 0:
            input_features[43] = rolloff_idx[0] / 256.0

        # 频谱平坦度
        geometric_mean = np.exp(np.mean(np.log(fft_magnitude + 1e-10)))
        arithmetic_mean = np.mean(fft_magnitude)
        input_features[44] = geometric_mean / (arithmetic_mean + 1e-10)

        # 频带能量 (20维)
        for j in range(20):
            start_bin = int(j * 256 / 20)
            end_bin = int((j + 1) * 256 / 20)
            input_features[45 + j] = np.mean(fft_magnitude[start_bin:end_bin])

        return input_features.astype(np.float32)

    except Exception as e:
        print(f"单帧特征提取失败: {e}")
        return None

def process_with_rnnoise_model(model, features):
    """使用RNNoise模型处理"""
    print("使用RNNoise模型处理...")

    # 转换为PyTorch张量
    features_tensor = torch.FloatTensor(features).unsqueeze(0)  # 添加batch维度

    with torch.no_grad():
        try:
            # 运行模型
            output = model(features_tensor)

            if isinstance(output, tuple) and len(output) >= 2:
                # 模型输出 (gain, vad, states)
                pred_gain = output[0]
                pred_vad = output[1]

                # 获取增益和VAD
                gain = pred_gain.squeeze().numpy()
                vad = pred_vad.squeeze().numpy()

                print(f"模型处理完成: gain shape={gain.shape}, vad shape={vad.shape}")
                print(f"平均增益: {np.mean(gain):.3f}, 平均VAD: {np.mean(vad):.3f}")
                print(f"增益范围: [{np.min(gain):.3f}, {np.max(gain):.3f}]")

                return gain, vad
            else:
                print(f"模型输出格式不符合预期: {type(output)}")
                return None, None

        except Exception as e:
            print(f"模型处理失败: {e}")
            import traceback
            traceback.print_exc()
            return None, None

def apply_gain_to_audio(audio_data, gain, frame_size=480):
    """将增益掩码应用到音频（频域处理）"""
    print("应用增益掩码到音频...")

    # 归一化音频数据
    audio_data = audio_data.astype(np.float32) / 32768.0

    processed_audio = np.zeros_like(audio_data)
    num_frames = min(len(gain), len(audio_data) // frame_size)

    print(f"处理 {num_frames} 帧，增益形状: {gain.shape}")

    for i in range(num_frames):
        start_idx = i * frame_size
        end_idx = start_idx + frame_size

        if end_idx > len(audio_data):
            # 最后一帧用零填充
            frame = np.zeros(frame_size, dtype=np.float32)
            frame[:len(audio_data) - start_idx] = audio_data[start_idx:]
        else:
            frame = audio_data[start_idx:end_idx]

        # 频域处理
        fft_frame = np.fft.fft(frame, n=512)
        fft_magnitude = np.abs(fft_frame)
        fft_phase = np.angle(fft_frame)

        # 应用增益掩码
        if len(gain.shape) == 2:  # 如果增益是2D (frames, bands)
            frame_gain = gain[i]  # 获取当前帧的增益向量

            # 将32维增益映射到256个频率bin
            enhanced_magnitude = np.zeros_like(fft_magnitude)
            for band in range(len(frame_gain)):
                start_bin = int(band * 256 / len(frame_gain))
                end_bin = int((band + 1) * 256 / len(frame_gain))

                # 应用增益到对应频带
                band_gain = np.clip(frame_gain[band], 0.0, 1.0)
                enhanced_magnitude[start_bin:end_bin] = fft_magnitude[start_bin:end_bin] * band_gain

            # 对称处理负频率部分 (512点FFT，前256是正频率，后256是负频率)
            if len(enhanced_magnitude) == 512:
                enhanced_magnitude[256:] = enhanced_magnitude[255:0:-1]  # 镜像对称

        else:  # 如果增益是1D，应用到整个频谱
            frame_gain = np.clip(gain[i], 0.0, 1.0)
            enhanced_magnitude = fft_magnitude * frame_gain

        # 重构信号
        enhanced_fft = enhanced_magnitude * np.exp(1j * fft_phase)
        enhanced_frame = np.real(np.fft.ifft(enhanced_fft))

        # 取前frame_size个样本
        processed_frame = enhanced_frame[:frame_size]

        if end_idx > len(audio_data):
            processed_audio[start_idx:] = processed_frame[:len(audio_data) - start_idx]
        else:
            processed_audio[start_idx:end_idx] = processed_frame

    # 转换回int16范围
    processed_audio = np.clip(processed_audio * 32768.0, -32768, 32767).astype(np.int16)

    print(f"增益应用完成，处理了 {num_frames} 帧")
    return processed_audio

def analyze_audio_quality(original, processed):
    """分析音频质量"""
    print("\n=== 音频质量分析 ===")
    
    # 计算RMS能量
    original_rms = np.sqrt(np.mean(original**2))
    processed_rms = np.sqrt(np.mean(processed**2))
    
    # 计算降噪效果
    if processed_rms > 0:
        noise_reduction = 20 * np.log10(original_rms / processed_rms)
    else:
        noise_reduction = float('inf')
    
    # 计算静音帧比例
    silence_threshold = 100
    original_silence = np.sum(np.abs(original) < silence_threshold) / len(original)
    processed_silence = np.sum(np.abs(processed) < silence_threshold) / len(processed)
    
    print(f"原始音频RMS: {original_rms:.2f}")
    print(f"处理音频RMS: {processed_rms:.2f}")
    print(f"降噪效果: {noise_reduction:.2f} dB")
    print(f"原始静音比例: {original_silence:.2%}")
    print(f"处理静音比例: {processed_silence:.2%}")

def main():
    print("🎯 PyTorch RNNoise模型测试")
    print("="*50)
    
    # 检查文件
    print("\n=== 检查文件 ===")
    #########################################################################################################
    test_audio = Path("20250716_084323.wav")
    ffmpeg_path = Path("../../env/ffmpeg.exe")
    
    if not test_audio.exists():
        print(f"✗ 测试音频不存在: {test_audio}")
        return False
    
    if not ffmpeg_path.exists():
        print(f"✗ FFmpeg不存在: {ffmpeg_path}")
        return False
    
    print(f"✓ 测试音频: {test_audio} ({test_audio.stat().st_size:,} 字节)")
    print(f"✓ FFmpeg: {ffmpeg_path}")
    
    # 加载音频
    print("\n=== 加载音频 ===")
    audio_data = load_audio_with_ffmpeg(str(test_audio), str(ffmpeg_path))
    if audio_data is None:
        return False
    
    # 加载RNNoise模型
    model = load_rnnoise_model()
    
    if model is not None:
        print("\n=== 使用RNNoise模型处理 ===")
        
        # 提取RNNoise标准特征
        features = extract_rnnoise_features(audio_data)
        
        # 使用模型处理
        gain, vad = process_with_rnnoise_model(model, features)
        
        if gain is not None:
            # 应用增益
            processed_audio = apply_gain_to_audio(audio_data, gain)
            output_file = "output_pytorch_rnnoise.wav"
        else:
            print("⚠ 模型处理失败，使用简单算法")
            processed_audio = audio_data * 0.8  # 简单衰减
            output_file = "output_simple_processed.wav"
    else:
        print("\n=== 使用简单处理算法 ===")
        processed_audio = audio_data * 0.8  # 简单衰减
        output_file = "output_simple_processed.wav"
    
    # 分析质量
    analyze_audio_quality(audio_data, processed_audio)
    
    # 保存结果
    print(f"\n=== 保存结果 ===")
    success = save_audio_with_ffmpeg(processed_audio, output_file, str(ffmpeg_path))
    
    if not success:
        return False
    
    # 生成报告
    print("\n" + "="*50)
    print("🎉 PyTorch RNNoise测试完成！")
    print("="*50)
    
    print(f"\n📁 生成的文件:")
    print(f"  原始音频: {test_audio}")
    print(f"  处理音频: {output_file}")
    
    if Path(output_file).exists():
        output_size = Path(output_file).stat().st_size
        print(f"  输出大小: {output_size:,} 字节")
    
    print(f"\n🎵 测试结果:")
    if model is not None:
        print("1. ✅ 成功使用了PyTorch RNNoise模型")
        print("2. 🎯 这是基于您训练模型的真实降噪效果")
    else:
        print("1. ⚠ 未能加载RNNoise模型，使用了简单处理")
        print("2. 💡 请确保rnnoise.py文件存在且模型文件完整")
    
    print("3. 🎧 请使用音频播放器比较两个文件的效果")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎊 PyTorch RNNoise测试成功完成！")
        else:
            print("\n❌ PyTorch RNNoise测试失败")
            sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
