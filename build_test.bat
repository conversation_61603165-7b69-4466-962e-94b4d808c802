@echo off
REM RNNoise 自定义模型编译脚本
REM 用于Windows环境下编译测试程序

echo ========================================
echo RNNoise 自定义模型编译脚本
echo ========================================

REM 检查编译器
where gcc >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到GCC编译器
    echo 请安装MinGW或MSYS2
    pause
    exit /b 1
)

echo 找到GCC编译器: 
gcc --version | findstr gcc

REM 设置编译参数
set CFLAGS=-O3 -Wall -Wextra -std=c99
set INCLUDES=-Iinclude -Isrc
set LIBS=-lm

REM 源文件列表
set SOURCES=src/denoise.c src/rnn.c src/pitch.c src/kiss_fft.c src/celt_lpc.c src/nnet.c src/nnet_default.c src/parse_lpcnet_weights.c src/rnnoise_tables.c

REM 检查是否有自定义模型数据
if exist "torch\rnnoise\rnnoise_c\rnnoise_data.c" (
    echo 找到自定义模型数据文件
    set CUSTOM_MODEL=torch\rnnoise\rnnoise_c\rnnoise_data.c
    set INCLUDES=%INCLUDES% -Itorchoise\rnnoise\rnnoise_c
) else (
    echo 使用默认模型数据
    set CUSTOM_MODEL=src\rnnoise_data.c
)

echo.
echo 编译参数:
echo CFLAGS: %CFLAGS%
echo INCLUDES: %INCLUDES%
echo LIBS: %LIBS%
echo 自定义模型: %CUSTOM_MODEL%

echo.
echo ========================================
echo 步骤1: 编译RNNoise库
echo ========================================

gcc %CFLAGS% %INCLUDES% -c %SOURCES% %CUSTOM_MODEL%
if %errorlevel% neq 0 (
    echo 编译RNNoise库失败
    pause
    exit /b 1
)

echo RNNoise库编译成功

echo.
echo ========================================
echo 步骤2: 编译测试程序
echo ========================================

gcc %CFLAGS% %INCLUDES% -o test_custom_model.exe test_custom_model.c *.o %LIBS%
if %errorlevel% neq 0 (
    echo 编译测试程序失败
    pause
    exit /b 1
)

echo 测试程序编译成功: test_custom_model.exe

echo.
echo ========================================
echo 步骤3: 编译原始示例程序
echo ========================================

gcc %CFLAGS% %INCLUDES% -o rnnoise_demo.exe examples/rnnoise_demo.c *.o %LIBS%
if %errorlevel% neq 0 (
    echo 编译示例程序失败
    pause
    exit /b 1
)

echo 示例程序编译成功: rnnoise_demo.exe

echo.
echo ========================================
echo 编译完成！
echo ========================================

echo 生成的程序:
echo   test_custom_model.exe - 自定义模型测试程序
echo   rnnoise_demo.exe      - 原始示例程序

echo.
echo 使用方法:
echo   test_custom_model.exe input.pcm output.pcm
echo   rnnoise_demo.exe input.pcm output.pcm

echo.
echo 测试建议:
echo 1. 使用noise_voice目录中的音频文件作为输入
echo 2. 比较原始程序和自定义模型的效果
echo 3. 使用ffmpeg将PCM转换为WAV格式试听

REM 清理临时文件
del *.o 2>nul

echo.
echo 按任意键退出...
pause >nul
