#!/bin/bash

# RNNoise 自定义模型编译脚本
# 用于Linux/macOS环境下编译测试程序

echo "========================================"
echo "RNNoise 自定义模型编译脚本"
echo "========================================"

# 检查编译器
if ! command -v gcc &> /dev/null; then
    echo "错误: 未找到GCC编译器"
    echo "请安装GCC: sudo apt-get install gcc (Ubuntu) 或 brew install gcc (macOS)"
    exit 1
fi

echo "找到GCC编译器:"
gcc --version | head -1

# 设置编译参数
CFLAGS="-O3 -Wall -Wextra -std=c99"
INCLUDES="-Iinclude -Isrc"
LIBS="-lm"

# 源文件列表
SOURCES="src/denoise.c src/rnn.c src/pitch.c src/kiss_fft.c src/celt_lpc.c src/nnet.c src/nnet_default.c src/parse_lpcnet_weights.c src/rnnoise_tables.c"

# 检查是否有自定义模型数据
if [ -f "torch/rnnoise/rnnoise_c/rnnoise_data.c" ]; then
    echo "找到自定义模型数据文件"
    CUSTOM_MODEL="torch/rnnoise/rnnoise_c/rnnoise_data.c"
    INCLUDES="$INCLUDES -Itorchoise/rnnoise/rnnoise_c"
else
    echo "使用默认模型数据"
    CUSTOM_MODEL="src/rnnoise_data.c"
fi

echo ""
echo "编译参数:"
echo "CFLAGS: $CFLAGS"
echo "INCLUDES: $INCLUDES"
echo "LIBS: $LIBS"
echo "自定义模型: $CUSTOM_MODEL"

echo ""
echo "========================================"
echo "步骤1: 编译RNNoise库"
echo "========================================"

gcc $CFLAGS $INCLUDES -c $SOURCES $CUSTOM_MODEL
if [ $? -ne 0 ]; then
    echo "编译RNNoise库失败"
    exit 1
fi

echo "RNNoise库编译成功"

echo ""
echo "========================================"
echo "步骤2: 编译测试程序"
echo "========================================"

gcc $CFLAGS $INCLUDES -o test_custom_model test_custom_model.c *.o $LIBS
if [ $? -ne 0 ]; then
    echo "编译测试程序失败"
    exit 1
fi

echo "测试程序编译成功: test_custom_model"

echo ""
echo "========================================"
echo "步骤3: 编译原始示例程序"
echo "========================================"

gcc $CFLAGS $INCLUDES -o rnnoise_demo examples/rnnoise_demo.c *.o $LIBS
if [ $? -ne 0 ]; then
    echo "编译示例程序失败"
    exit 1
fi

echo "示例程序编译成功: rnnoise_demo"

echo ""
echo "========================================"
echo "编译完成！"
echo "========================================"

echo "生成的程序:"
echo "  test_custom_model - 自定义模型测试程序"
echo "  rnnoise_demo      - 原始示例程序"

echo ""
echo "使用方法:"
echo "  ./test_custom_model input.pcm output.pcm"
echo "  ./rnnoise_demo input.pcm output.pcm"

echo ""
echo "测试建议:"
echo "1. 使用noise_voice目录中的音频文件作为输入"
echo "2. 比较原始程序和自定义模型的效果"
echo "3. 使用ffmpeg将PCM转换为WAV格式试听"

# 清理临时文件
rm -f *.o

# 设置执行权限
chmod +x test_custom_model rnnoise_demo

echo ""
echo "编译脚本执行完成！"
