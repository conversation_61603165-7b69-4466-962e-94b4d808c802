#!/usr/bin/env python3
"""
简化的C语言测试 - 使用现有工具
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(cmd, cwd=None, shell=True):
    """运行命令"""
    try:
        print(f"执行: {' '.join(cmd) if isinstance(cmd, list) else cmd}")
        result = subprocess.run(cmd, cwd=cwd, shell=shell, 
                              capture_output=True, text=True, check=True)
        return True, result.stdout, result.stderr
    except subprocess.CalledProcessError as e:
        return False, e.stdout, e.stderr

def check_visual_studio():
    """检查Visual Studio编译器"""
    print("\n=== 检查Visual Studio编译器 ===")
    
    # 常见的Visual Studio路径
    vs_paths = [
        "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC",
        "C:/Program Files/Microsoft Visual Studio/2019/Community/VC/Tools/MSVC",
        "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC",
        "C:/Program Files (x86)/Microsoft Visual Studio/2017/BuildTools/VC/Tools/MSVC"
    ]
    
    for vs_path in vs_paths:
        if Path(vs_path).exists():
            # 查找cl.exe
            for version_dir in Path(vs_path).iterdir():
                cl_exe = version_dir / "bin" / "Hostx64" / "x64" / "cl.exe"
                if cl_exe.exists():
                    print(f"✓ 找到Visual Studio编译器: {cl_exe}")
                    return str(cl_exe)
    
    # 检查是否在PATH中
    success, stdout, stderr = run_command(["cl"])
    if success or "Microsoft" in stderr:
        print("✓ 找到系统中的cl.exe")
        return "cl"
    
    print("✗ 未找到Visual Studio编译器")
    return None

def try_online_compiler():
    """尝试使用在线编译服务"""
    print("\n=== 尝试在线编译 ===")
    
    # 创建一个简单的测试程序
    test_c = """
#include <stdio.h>
#include <stdlib.h>
#include <math.h>

// 简化的RNNoise处理函数
void simple_denoise(short* input, short* output, int length) {
    for (int i = 0; i < length; i++) {
        float sample = (float)input[i];
        
        // 简单的噪声门限
        if (abs(input[i]) < 500) {
            sample *= 0.3f;  // 衰减低能量信号
        }
        
        // 简单的平滑滤波
        if (i > 0 && i < length - 1) {
            sample = 0.25f * input[i-1] + 0.5f * sample + 0.25f * input[i+1];
        }
        
        output[i] = (short)sample;
    }
}

int main(int argc, char** argv) {
    if (argc != 3) {
        printf("Usage: %s <input.pcm> <output.pcm>\\n", argv[0]);
        return 1;
    }
    
    FILE* input_file = fopen(argv[1], "rb");
    if (!input_file) {
        printf("Error: Cannot open input file %s\\n", argv[1]);
        return 1;
    }
    
    // 获取文件大小
    fseek(input_file, 0, SEEK_END);
    long file_size = ftell(input_file);
    fseek(input_file, 0, SEEK_SET);
    
    int sample_count = file_size / sizeof(short);
    printf("Processing %d samples (%.2f seconds)\\n", sample_count, (float)sample_count / 48000);
    
    // 分配内存
    short* input_buffer = (short*)malloc(file_size);
    short* output_buffer = (short*)malloc(file_size);
    
    if (!input_buffer || !output_buffer) {
        printf("Error: Memory allocation failed\\n");
        return 1;
    }
    
    // 读取数据
    fread(input_buffer, sizeof(short), sample_count, input_file);
    fclose(input_file);
    
    // 处理音频
    printf("Applying simple denoising...\\n");
    simple_denoise(input_buffer, output_buffer, sample_count);
    
    // 写入结果
    FILE* output_file = fopen(argv[2], "wb");
    if (!output_file) {
        printf("Error: Cannot create output file %s\\n", argv[2]);
        return 1;
    }
    
    fwrite(output_buffer, sizeof(short), sample_count, output_file);
    fclose(output_file);
    
    printf("Denoising completed successfully!\\n");
    printf("Output saved to: %s\\n", argv[2]);
    
    free(input_buffer);
    free(output_buffer);
    
    return 0;
}
"""
    
    # 保存测试程序
    test_file = Path("simple_denoise.c")
    with open(test_file, 'w') as f:
        f.write(test_c)
    
    print(f"✓ 创建了简化的降噪程序: {test_file}")
    return str(test_file)

def compile_with_tcc():
    """使用TCC编译器"""
    print("\n=== 尝试使用TCC编译器 ===")
    
    # 下载TCC (Tiny C Compiler)
    import urllib.request
    
    tcc_url = "https://download.savannah.gnu.org/releases/tinycc/tcc-0.9.27-win64-bin.zip"
    tcc_zip = Path("env/tcc.zip")
    
    try:
        print("下载TCC编译器...")
        urllib.request.urlretrieve(tcc_url, tcc_zip)
        
        import zipfile
        with zipfile.ZipFile(tcc_zip, 'r') as zip_ref:
            zip_ref.extractall("env")
        
        tcc_exe = Path("env/tcc/tcc.exe")
        if tcc_exe.exists():
            print(f"✓ TCC安装成功: {tcc_exe}")
            return str(tcc_exe)
    
    except Exception as e:
        print(f"✗ TCC下载失败: {e}")
    
    return None

def test_simple_denoise():
    """测试简化的降噪程序"""
    print("\n=== 测试简化降噪程序 ===")
    
    # 创建简化的降噪程序
    program_file = try_online_compiler()
    
    # 尝试不同的编译器
    compilers = []
    
    # 检查Visual Studio
    vs_compiler = check_visual_studio()
    if vs_compiler:
        compilers.append(("Visual Studio", vs_compiler))
    
    # 尝试TCC
    tcc_compiler = compile_with_tcc()
    if tcc_compiler:
        compilers.append(("TCC", tcc_compiler))
    
    if not compilers:
        print("✗ 没有可用的编译器")
        return False
    
    # 尝试编译
    for compiler_name, compiler_path in compilers:
        print(f"\n--- 尝试使用 {compiler_name} 编译 ---")
        
        if compiler_name == "Visual Studio":
            compile_cmd = [compiler_path, "/Fe:simple_denoise.exe", program_file]
        elif compiler_name == "TCC":
            compile_cmd = [compiler_path, "-o", "simple_denoise.exe", program_file]
        else:
            compile_cmd = [compiler_path, "-o", "simple_denoise.exe", program_file, "-lm"]
        
        success, stdout, stderr = run_command(compile_cmd)
        
        if success:
            print(f"✓ 使用 {compiler_name} 编译成功")
            
            # 测试程序
            return test_audio_with_program("simple_denoise.exe")
        else:
            print(f"✗ {compiler_name} 编译失败: {stderr}")
    
    return False

def test_audio_with_program(program_exe):
    """使用程序测试音频"""
    print(f"\n=== 使用 {program_exe} 测试音频 ===")
    
    # 检查测试音频
    test_audio = Path("torch/rnnoise/3.wav")
    if not test_audio.exists():
        print(f"✗ 测试音频不存在: {test_audio}")
        return False
    
    # 检查ffmpeg
    ffmpeg = Path("env/ffmpeg.exe")
    if not ffmpeg.exists():
        print(f"✗ FFmpeg不存在: {ffmpeg}")
        return False
    
    # 转换音频为PCM
    input_pcm = "test_input.pcm"
    convert_cmd = [str(ffmpeg), "-i", str(test_audio), "-f", "s16le", 
                  "-ar", "48000", "-ac", "1", "-y", input_pcm]
    
    success, stdout, stderr = run_command(convert_cmd)
    if not success:
        print(f"✗ 音频转换失败: {stderr}")
        return False
    
    input_size = Path(input_pcm).stat().st_size
    duration = input_size / (48000 * 2)
    print(f"✓ 音频转换成功: {input_pcm} ({input_size:,} 字节, {duration:.2f}秒)")
    
    # 运行降噪程序
    output_pcm = "output_simple_denoised.pcm"
    denoise_cmd = [f"./{program_exe}", input_pcm, output_pcm]
    
    import time
    start_time = time.time()
    success, stdout, stderr = run_command(denoise_cmd)
    end_time = time.time()
    
    if not success:
        print(f"✗ 降噪处理失败: {stderr}")
        return False
    
    processing_time = end_time - start_time
    output_size = Path(output_pcm).stat().st_size if Path(output_pcm).exists() else 0
    
    print(f"✓ 降噪处理成功")
    print(f"  处理时间: {processing_time:.2f}秒")
    print(f"  输出大小: {output_size:,} 字节")
    print(f"  实时倍数: {duration/processing_time:.2f}x" if processing_time > 0 else "")
    
    # 转换结果为WAV
    output_wav = "output_simple_denoised.wav"
    wav_cmd = [str(ffmpeg), "-f", "s16le", "-ar", "48000", "-ac", "1", 
              "-i", output_pcm, "-y", output_wav]
    
    success, stdout, stderr = run_command(wav_cmd)
    if success:
        wav_size = Path(output_wav).stat().st_size
        print(f"✓ WAV转换成功: {output_wav} ({wav_size:,} 字节)")
    
    return True

def main():
    print("🎯 简化C语言降噪测试")
    print("="*50)
    
    print("由于编译器安装复杂，我们使用简化的降噪算法进行演示")
    
    try:
        success = test_simple_denoise()
        
        if success:
            print("\n" + "="*50)
            print("🎉 简化C语言测试完成！")
            print("="*50)
            
            print(f"\n📁 生成的文件:")
            result_files = [
                ("C程序源码", "simple_denoise.c"),
                ("编译程序", "simple_denoise.exe"),
                ("输出PCM", "output_simple_denoised.pcm"),
                ("输出WAV", "output_simple_denoised.wav")
            ]
            
            for name, file in result_files:
                if Path(file).exists():
                    size = Path(file).stat().st_size
                    print(f"  {name}: {file} ({size:,} 字节)")
            
            print(f"\n🎵 音频对比:")
            print(f"  原始音频: torch/rnnoise/3.wav")
            print(f"  简化C降噪: output_simple_denoised.wav")
            print(f"  PyTorch降噪: torch/rnnoise/output_pytorch_rnnoise.wav")
            
            print(f"\n📝 说明:")
            print("1. ✅ 成功创建并编译了C语言降噪程序")
            print("2. ⚠ 这是简化版本，不是真正的RNNoise算法")
            print("3. 🎯 要使用您的自定义RNNoise模型，需要安装完整的GCC编译器")
            
            return True
        else:
            print("\n❌ 简化测试也失败了")
            return False
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎊 简化C语言测试成功！")
        print("\n💡 要使用真正的RNNoise C语言模型:")
        print("1. 安装MinGW: https://www.mingw-w64.org/")
        print("2. 或安装Visual Studio Build Tools")
        print("3. 然后运行完整的编译测试脚本")
    else:
        print("\n😞 测试失败")
        sys.exit(1)
