#!/usr/bin/env python3
"""
简化的RNNoise模型测试脚本
不需要编译C代码，直接使用Python进行模型测试
"""

import os
import sys
import subprocess
import numpy as np
import torch
from pathlib import Path

# 添加torch/rnnoise到路径
sys.path.append('torch/rnnoise')

class SimpleModelTester:
    """简化的模型测试器"""
    
    def __init__(self):
        self.base_dir = Path(".")
        self.model_dir = Path("torch/rnnoise/rnnoise_c")
        self.test_audio = Path("torch/rnnoise/3.wav")
        
        print(f"工作目录: {os.getcwd()}")
        print(f"模型目录: {self.model_dir}")
        print(f"测试音频: {self.test_audio}")
    
    def check_files(self):
        """检查文件是否存在"""
        print("\n=== 检查文件 ===")
        
        files_to_check = [
            ("模型头文件", self.model_dir / "rnnoise_data.h"),
            ("模型数据文件", self.model_dir / "rnnoise_data.c"),
            ("测试音频", self.test_audio),
            ("Python模型文件", "torch/rnnoise/rnnoise.py"),
        ]
        
        all_exist = True
        for name, path in files_to_check:
            if path.exists():
                if path.is_file():
                    size = path.stat().st_size
                    print(f"✓ {name}: {path} ({size} 字节)")
                else:
                    print(f"✓ {name}: {path} (目录)")
            else:
                print(f"✗ {name}: {path} (不存在)")
                all_exist = False
        
        return all_exist
    
    def check_python_environment(self):
        """检查Python环境"""
        print("\n=== 检查Python环境 ===")
        
        try:
            import torch
            print(f"✓ PyTorch版本: {torch.__version__}")
        except ImportError:
            print("✗ PyTorch未安装")
            return False
        
        try:
            import numpy
            print(f"✓ NumPy版本: {numpy.__version__}")
        except ImportError:
            print("✗ NumPy未安装")
            return False
        
        return True
    
    def load_pytorch_model(self):
        """加载PyTorch模型"""
        print("\n=== 加载PyTorch模型 ===")
        
        # 查找模型文件
        model_files = []
        for pattern in ["*.pth", "best_model.pth", "rnnoise_*.pth"]:
            model_files.extend(list(Path(".").rglob(pattern)))
        
        if not model_files:
            print("✗ 未找到PyTorch模型文件(.pth)")
            return None
        
        # 选择最新的模型文件
        latest_model = max(model_files, key=lambda x: x.stat().st_mtime)
        print(f"找到模型文件: {latest_model}")
        
        try:
            # 尝试加载模型
            checkpoint = torch.load(latest_model, map_location='cpu')
            print(f"✓ 模型加载成功")
            
            # 检查模型结构
            if 'model_kwargs' in checkpoint:
                print(f"模型参数: {checkpoint['model_kwargs']}")
            
            if 'state_dict' in checkpoint:
                print(f"权重参数数量: {len(checkpoint['state_dict'])}")
            
            return checkpoint
        except Exception as e:
            print(f"✗ 模型加载失败: {e}")
            return None
    
    def convert_audio_to_numpy(self):
        """将音频转换为numpy数组"""
        print("\n=== 处理音频文件 ===")
        
        # 查找ffmpeg
        ffmpeg_paths = ["env/ffmpeg.exe", "ffmpeg.exe", "ffmpeg"]
        ffmpeg = None
        
        for path in ffmpeg_paths:
            if Path(path).exists():
                ffmpeg = path
                break
        
        if not ffmpeg:
            print("✗ 未找到ffmpeg")
            return None
        
        print(f"使用ffmpeg: {ffmpeg}")
        
        # 转换为PCM
        pcm_file = "temp_audio.pcm"
        cmd = [
            ffmpeg,
            "-i", str(self.test_audio),
            "-f", "s16le",
            "-ar", "48000",
            "-ac", "1",
            "-y",
            pcm_file
        ]
        
        try:
            subprocess.run(cmd, capture_output=True, check=True)
            print(f"✓ 音频转换成功: {pcm_file}")
            
            # 读取PCM数据
            audio_data = np.fromfile(pcm_file, dtype=np.int16)
            print(f"音频长度: {len(audio_data)} 样本 ({len(audio_data)/48000:.2f}秒)")
            
            # 转换为浮点数
            audio_float = audio_data.astype(np.float32)
            
            return audio_float
            
        except subprocess.CalledProcessError as e:
            print(f"✗ 音频转换失败: {e}")
            return None
    
    def test_c_model_data(self):
        """测试C模型数据文件"""
        print("\n=== 分析C模型数据 ===")
        
        h_file = self.model_dir / "rnnoise_data.h"
        c_file = self.model_dir / "rnnoise_data.c"
        
        if not (h_file.exists() and c_file.exists()):
            print("✗ C模型文件不完整")
            return False
        
        try:
            # 读取头文件
            with open(h_file, 'r', encoding='utf-8') as f:
                h_content = f.read()
            
            print("✓ 头文件内容分析:")
            
            # 提取关键定义
            definitions = []
            for line in h_content.split('\n'):
                if line.strip().startswith('#define') and 'SIZE' in line:
                    definitions.append(line.strip())
            
            for def_line in definitions[:10]:  # 显示前10个定义
                print(f"  {def_line}")
            
            # 检查C文件大小
            c_size = c_file.stat().st_size
            print(f"✓ 数据文件大小: {c_size} 字节 ({c_size/1024/1024:.2f} MB)")
            
            if c_size > 1000000:  # 大于1MB
                print("✓ 模型数据文件大小合理")
            else:
                print("⚠ 模型数据文件可能不完整")
            
            return True
            
        except Exception as e:
            print(f"✗ 分析C模型数据失败: {e}")
            return False
    
    def generate_test_report(self):
        """生成测试报告"""
        print("\n" + "="*50)
        print("📋 测试报告")
        print("="*50)
        
        # 文件检查结果
        files_ok = self.check_files()
        
        # Python环境检查
        python_ok = self.check_python_environment()
        
        # 模型加载测试
        model = self.load_pytorch_model() if python_ok else None
        
        # 音频处理测试
        audio = self.convert_audio_to_numpy()
        
        # C模型数据分析
        c_model_ok = self.test_c_model_data()
        
        print(f"\n📊 测试结果总结:")
        print(f"文件完整性: {'✓' if files_ok else '✗'}")
        print(f"Python环境: {'✓' if python_ok else '✗'}")
        print(f"PyTorch模型: {'✓' if model else '✗'}")
        print(f"音频处理: {'✓' if audio is not None else '✗'}")
        print(f"C模型数据: {'✓' if c_model_ok else '✗'}")
        
        print(f"\n🎯 下一步建议:")
        
        if not files_ok:
            print("1. 确保所有必要文件都存在")
        
        if not python_ok:
            print("2. 安装必要的Python包: pip install torch numpy")
        
        if files_ok and c_model_ok:
            print("3. 您的C模型数据已准备就绪")
            print("4. 可以尝试编译C程序进行测试")
            print("5. 安装GCC编译器: https://www.mingw-w64.org/")
            
        if audio is not None:
            print("6. 音频处理正常，可以进行降噪测试")
        
        print(f"\n📁 相关文件:")
        print(f"- C模型头文件: {self.model_dir / 'rnnoise_data.h'}")
        print(f"- C模型数据: {self.model_dir / 'rnnoise_data.c'}")
        print(f"- 测试音频: {self.test_audio}")
        print(f"- 编译脚本: build_test.bat (Windows) 或 build_test.sh (Linux)")
        print(f"- 测试程序: test_custom_model.c")
        
        return files_ok and c_model_ok
    
    def run_test(self):
        """运行测试"""
        print("🔍 RNNoise模型简化测试")
        print("="*50)
        
        success = self.generate_test_report()
        
        if success:
            print("\n🎉 基础检查通过！您的模型文件已准备就绪。")
            print("\n要进行完整的C语言测试，请:")
            print("1. 安装GCC编译器")
            print("2. 运行: python test_your_model.py")
        else:
            print("\n⚠ 发现一些问题，请根据上述建议进行修复。")
        
        return success

def main():
    tester = SimpleModelTester()
    tester.run_test()

if __name__ == "__main__":
    main()
