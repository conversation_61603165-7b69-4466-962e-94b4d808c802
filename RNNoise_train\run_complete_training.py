#!/usr/bin/env python3
"""
完整的RNNoise训练流程
包含训练和验证的完整流程
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path

def check_dependencies():
    """检查依赖项"""
    print("=== 检查依赖项 ===")
    
    # 检查Python包
    required_packages = ['torch', 'numpy', 'tqdm']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package} (缺失)")
    
    if missing_packages:
        print(f"\n请安装缺失的包: pip install {' '.join(missing_packages)}")
        return False
    
    # 检查特征文件
    features_file = "features/original_training_features.f32"
    if os.path.exists(features_file):
        file_size = os.path.getsize(features_file)
        print(f"✓ 特征文件: {features_file} ({file_size / (1024*1024):.2f} MB)")
    else:
        print(f"✗ 特征文件不存在: {features_file}")
        print("请先运行特征提取: python extract_original_features.py")
        return False
    
    # 检查测试音频
    test_audio_dir = "test_voice"
    if os.path.exists(test_audio_dir):
        audio_files = [f for f in os.listdir(test_audio_dir) if f.endswith('.wav')]
        print(f"✓ 测试音频目录: {test_audio_dir} ({len(audio_files)} 个WAV文件)")
    else:
        print(f"⚠️ 测试音频目录不存在: {test_audio_dir}")
        print("将跳过降噪效果测试")
    
    return True

def run_training(args):
    """运行训练"""
    print("\n=== 开始训练 ===")
    
    cmd = [
        sys.executable, 'train_rnnoise_original.py',
        '--features', args.features,
        '--output', args.output,
        '--epochs', str(args.epochs),
        '--batch-size', str(args.batch_size),
        '--lr', str(args.lr),
        '--sequence-length', str(args.sequence_length),
        '--lr-decay', str(args.lr_decay),
        '--gamma', str(args.gamma),
        '--cond-size', str(args.cond_size),
        '--gru-size', str(args.gru_size),
        '--suffix', args.suffix
    ]
    
    if args.initial_checkpoint:
        cmd.extend(['--initial-checkpoint', args.initial_checkpoint])
    
    if args.sparse:
        cmd.append('--sparse')
    
    if args.cuda_visible_devices:
        cmd.extend(['--cuda-visible-devices', args.cuda_visible_devices])
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, cwd=os.path.dirname(__file__))
        print("✓ 训练完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 训练失败: {e}")
        return False

def find_latest_model(output_dir, suffix):
    """查找最新的模型文件"""
    checkpoints_dir = os.path.join(output_dir, 'checkpoints')
    
    if not os.path.exists(checkpoints_dir):
        return None
    
    model_files = [f for f in os.listdir(checkpoints_dir) if f.startswith(f'rnnoise{suffix}_') and f.endswith('.pth')]
    
    if not model_files:
        return None
    
    # 按epoch排序，取最新的
    model_files.sort(key=lambda x: int(x.split('_')[-1].split('.')[0]))
    latest_model = os.path.join(checkpoints_dir, model_files[-1])
    
    return latest_model

def run_validation(model_path, test_audio_dir):
    """运行验证"""
    print("\n=== 开始验证 ===")
    
    cmd = [
        sys.executable, 'validate_model.py',
        '--model', model_path,
        '--test-audio-dir', test_audio_dir
    ]
    
    if not os.path.exists(test_audio_dir):
        cmd.append('--skip-denoise-test')
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, cwd=os.path.dirname(__file__))
        print("✓ 验证完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 验证失败: {e}")
        return False

def export_to_c(model_path, output_dir):
    """导出为C代码"""
    print("\n=== 导出C代码 ===")
    
    c_output_dir = os.path.join(output_dir, 'c_export')
    
    cmd = [
        sys.executable, 'dump_rnnoise_weights.py',
        '--quantize',
        model_path,
        c_output_dir
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, cwd=os.path.dirname(__file__))
        print(f"✓ C代码导出完成: {c_output_dir}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ C代码导出失败: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='完整的RNNoise训练流程')
    
    # 文件路径
    parser.add_argument('--features', type=str, 
                       default='features/original_training_features.f32',
                       help='特征文件路径')
    parser.add_argument('--output', type=str, 
                       default='model_output',
                       help='模型输出目录')
    parser.add_argument('--test-audio-dir', type=str, 
                       default='test_voice',
                       help='测试音频目录')
    
    # 训练参数
    parser.add_argument('--epochs', type=int, default=200,
                       help='训练轮次')
    parser.add_argument('--batch-size', type=int, default=128,
                       help='批次大小')
    parser.add_argument('--lr', type=float, default=1e-3,
                       help='学习率')
    parser.add_argument('--sequence-length', type=int, default=2000,
                       help='序列长度')
    parser.add_argument('--lr-decay', type=float, default=5e-5,
                       help='学习率衰减')
    parser.add_argument('--gamma', type=float, default=0.25,
                       help='感知指数')
    
    # 模型参数
    parser.add_argument('--cond-size', type=int, default=128,
                       help='条件层大小')
    parser.add_argument('--gru-size', type=int, default=384,
                       help='GRU层大小')
    
    # 可选参数
    parser.add_argument('--suffix', type=str, default='_custom',
                       help='模型名称后缀')
    parser.add_argument('--initial-checkpoint', type=str,
                       help='初始检查点文件路径')
    parser.add_argument('--sparse', action='store_true',
                       help='启用稀疏化训练')
    parser.add_argument('--cuda-visible-devices', type=str,
                       help='CUDA可见设备')
    
    # 流程控制
    parser.add_argument('--skip-training', action='store_true',
                       help='跳过训练，直接验证')
    parser.add_argument('--skip-validation', action='store_true',
                       help='跳过验证')
    parser.add_argument('--skip-export', action='store_true',
                       help='跳过C代码导出')
    
    args = parser.parse_args()
    
    try:
        print("🚀 RNNoise完整训练流程")
        print("=" * 50)
        
        # 检查依赖项
        if not check_dependencies():
            print("❌ 依赖项检查失败")
            sys.exit(1)
        
        # 训练
        if not args.skip_training:
            training_success = run_training(args)
            if not training_success:
                print("❌ 训练失败")
                sys.exit(1)
        
        # 查找最新模型
        latest_model = find_latest_model(args.output, args.suffix)
        if not latest_model:
            print("❌ 找不到训练好的模型")
            sys.exit(1)
        
        print(f"\n📦 使用模型: {latest_model}")
        
        # 验证
        if not args.skip_validation:
            validation_success = run_validation(latest_model, args.test_audio_dir)
            if not validation_success:
                print("⚠️ 验证失败，但继续执行")
        
        # 导出C代码
        if not args.skip_export:
            export_success = export_to_c(latest_model, args.output)
            if not export_success:
                print("⚠️ C代码导出失败")
        
        print("\n🎉 完整流程执行完成！")
        print("\n📁 输出文件:")
        print(f"  模型文件: {latest_model}")
        print(f"  验证结果: denoised_output/")
        print(f"  C代码: {args.output}/c_export/")
        
        print("\n📋 下一步:")
        print("1. 检查 denoised_output/ 中的降噪效果")
        print("2. 播放原始音频和降噪后音频进行对比")
        print("3. 如果效果满意，可以使用 c_export/ 中的C代码")
        
    except Exception as e:
        print(f"❌ 流程执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
