# RNNoise C语言模型测试指南

## 🎯 概述

您已经成功训练了自定义的RNNoise模型，并生成了C语言代码。本指南将帮助您测试这个C语言实现的效果。

## 📁 生成的文件

### C语言代码
- `torch/rnnoise/rnnoise_c/rnnoise_data.h` - 模型头文件
- `torch/rnnoise/rnnoise_c/rnnoise_data.c` - 模型权重数据

### 测试工具
- `test_custom_model.c` - 自定义模型测试程序
- `build_test.bat` / `build_test.sh` - 编译脚本
- `convert_audio.py` - 音频格式转换工具
- `run_c_test.py` - 完整测试流程脚本

## 🚀 快速开始

### 方法1: 一键测试 (推荐)

```bash
# 运行完整测试流程
python run_c_test.py
```

这个脚本会自动执行：
1. 编译C程序
2. 准备测试数据
3. 运行降噪测试
4. 转换结果为WAV格式
5. 分析测试结果

### 方法2: 分步执行

#### 步骤1: 编译程序

**Windows:**
```cmd
build_test.bat
```

**Linux/macOS:**
```bash
chmod +x build_test.sh
./build_test.sh
```

#### 步骤2: 准备测试数据

```bash
# 将WAV文件转换为PCM格式
python convert_audio.py --prepare-test --max-files 5
```

#### 步骤3: 运行测试

```bash
# 使用自定义模型测试
./test_custom_model test_pcm_input/xxx.pcm output_custom.pcm

# 使用原始模型对比
./rnnoise_demo test_pcm_input/xxx.pcm output_original.pcm
```

#### 步骤4: 转换结果

```bash
# 将PCM结果转换为WAV格式
python convert_audio.py --convert-results
```

## 📊 测试结果分析

### 输出文件
- `output_*.pcm` - PCM格式的降噪结果
- `test_results_wav/*.wav` - WAV格式的降噪结果

### 质量评估指标
测试程序会自动计算：
- **RMS能量**: 音频信号的均方根能量
- **降噪效果**: 以dB为单位的降噪程度
- **静音帧比例**: 被判定为静音的帧数比例
- **VAD概率**: 语音活动检测概率

### 效果对比
1. **听觉测试**: 使用音频播放器比较原始音频和降噪后音频
2. **波形对比**: 使用Audacity等工具查看波形变化
3. **频谱分析**: 观察频域上的噪声抑制效果

## 🔧 高级用法

### 自定义测试参数

```bash
# 测试特定文件
./test_custom_model input.pcm output.pcm

# 批量测试
for file in test_pcm_input/*.pcm; do
    output="output_$(basename "$file")"
    ./test_custom_model "$file" "$output"
done
```

### 性能测试

```bash
# 测试处理速度
time ./test_custom_model large_audio.pcm output.pcm
```

### 内存使用分析

```bash
# Linux下使用valgrind检查内存使用
valgrind --tool=massif ./test_custom_model input.pcm output.pcm
```

## 🐛 故障排除

### 编译问题

**问题**: 找不到头文件
```
fatal error: rnnoise.h: No such file or directory
```

**解决**: 确保include目录存在，检查编译脚本中的路径设置

**问题**: 链接错误
```
undefined reference to 'rnnoise_create'
```

**解决**: 确保所有源文件都被编译，检查库文件链接

### 运行时问题

**问题**: 程序崩溃或输出异常
- 检查输入文件格式是否正确（48kHz, 16位, 单声道PCM）
- 确保有足够的内存
- 验证模型数据文件完整性

**问题**: 降噪效果不佳
- 对比原始RNNoise模型的效果
- 检查训练数据质量
- 考虑重新训练模型

### 音频格式问题

**问题**: 音频格式不匹配
```bash
# 检查音频文件信息
ffprobe input.wav

# 转换为正确格式
ffmpeg -i input.wav -ar 48000 -ac 1 -f s16le output.pcm
```

## 📈 性能优化

### 编译优化
- 使用 `-O3` 优化级别
- 启用特定CPU指令集（AVX2, SSE4.1）
- 使用 `--enable-x86-rtcd` 配置选项

### 运行时优化
- 批量处理多个文件
- 使用多线程处理（需要修改代码）
- 内存映射大文件

## 🔄 集成到项目

### 静态库方式
```bash
# 编译为静态库
ar rcs librnnoise_custom.a *.o

# 在项目中链接
gcc -o myapp myapp.c -L. -lrnnoise_custom -lm
```

### 动态库方式
```bash
# 编译为动态库
gcc -shared -fPIC -o librnnoise_custom.so *.o -lm

# 在项目中使用
gcc -o myapp myapp.c -L. -lrnnoise_custom -lm
```

## 📝 API使用示例

```c
#include "rnnoise.h"

int main() {
    // 创建降噪状态
    DenoiseState *st = rnnoise_create(NULL);
    
    // 处理音频帧
    float frame[480];  // 480样本 = 10ms @ 48kHz
    float vad_prob = rnnoise_process_frame(st, frame, frame);
    
    // 清理资源
    rnnoise_destroy(st);
    return 0;
}
```

## 🎉 成功标志

如果看到以下输出，说明测试成功：
- ✅ 编译无错误
- ✅ 程序正常运行
- ✅ 生成降噪音频文件
- ✅ 音频质量分析显示合理的降噪效果
- ✅ WAV文件可以正常播放

## 📞 技术支持

如果遇到问题：
1. 检查编译器和依赖是否正确安装
2. 验证音频文件格式
3. 查看详细的错误信息
4. 对比原始RNNoise的行为

---

**祝您测试顺利！** 🎊
