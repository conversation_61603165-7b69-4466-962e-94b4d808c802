# RNNoise C语言模型手动测试指南

## 🎯 您的文件状态

根据检查，您已经有了：
- ✅ **C语言模型文件**: `rnnoise_c/rnnoise_data.h` 和 `rnnoise_c/rnnoise_data.c`
- ✅ **测试音频**: `3.wav`
- ✅ **RNNoise源代码**: 完整的C语言实现

## 🔧 需要安装的工具

### 1. C编译器 (必需)

**Windows用户推荐:**
- **MinGW-w64**: https://www.mingw-w64.org/downloads/
- **MSYS2**: https://www.msys2.org/ (推荐，包含完整工具链)
- **Visual Studio Build Tools**: https://visualstudio.microsoft.com/downloads/

**安装MSYS2 (推荐方法):**
1. 下载并安装 MSYS2
2. 打开 MSYS2 终端，运行：
```bash
pacman -S mingw-w64-x86_64-gcc
pacman -S mingw-w64-x86_64-make
```
3. 将 `C:\msys64\mingw64\bin` 添加到系统PATH

### 2. FFmpeg (音频转换)
您已经有了 `env/ffmpeg.exe`，很好！

## 🚀 手动测试步骤

### 步骤1: 准备模型文件

```cmd
# 在项目根目录 (D:\RNNoise\rnnoise-plus-main) 执行
cd D:\RNNoise\rnnoise-plus-main

# 备份原始模型文件
copy src\rnnoise_data.c src\rnnoise_data.c.backup

# 复制您的自定义模型文件
copy torch\rnnoise\rnnoise_c\rnnoise_data.h src\
copy torch\rnnoise\rnnoise_c\rnnoise_data.c src\
```

### 步骤2: 编译程序

```cmd
# 编译所有源文件
gcc -O3 -Wall -std=c99 -Iinclude -Isrc -c ^
    src/denoise.c ^
    src/rnn.c ^
    src/pitch.c ^
    src/kiss_fft.c ^
    src/celt_lpc.c ^
    src/nnet.c ^
    src/nnet_default.c ^
    src/parse_lpcnet_weights.c ^
    src/rnnoise_data.c ^
    src/rnnoise_tables.c

# 编译示例程序
gcc -O3 -Wall -std=c99 -Iinclude -Isrc -o rnnoise_demo.exe ^
    examples/rnnoise_demo.c ^
    *.o -lm
```

### 步骤3: 转换音频格式

```cmd
# 将WAV转换为PCM格式
env\ffmpeg.exe -i torch\rnnoise\3.wav -f s16le -ar 48000 -ac 1 -y test_input.pcm
```

### 步骤4: 运行降噪测试

```cmd
# 使用您的自定义模型进行降噪
rnnoise_demo.exe test_input.pcm output_denoised.pcm
```

### 步骤5: 转换结果为WAV

```cmd
# 将PCM结果转换为WAV格式
env\ffmpeg.exe -f s16le -ar 48000 -ac 1 -i output_denoised.pcm -y output_denoised.wav
```

## 📊 验证结果

### 检查文件大小
```cmd
dir test_input.pcm output_denoised.pcm output_denoised.wav
```

### 播放音频对比
1. **原始音频**: `torch\rnnoise\3.wav`
2. **降噪音频**: `output_denoised.wav`

使用Windows Media Player或其他音频播放器进行对比。

## 🐛 常见问题解决

### 编译错误

**问题1**: `'gcc' is not recognized`
```cmd
# 解决: 安装MinGW并添加到PATH
# 或使用完整路径
C:\msys64\mingw64\bin\gcc.exe --version
```

**问题2**: `fatal error: rnnoise.h: No such file or directory`
```cmd
# 解决: 确保在项目根目录执行编译命令
cd D:\RNNoise\rnnoise-plus-main
```

**问题3**: `undefined reference to 'rnnoise_create'`
```cmd
# 解决: 确保所有.o文件都被链接
# 重新编译所有源文件
```

### 运行时错误

**问题1**: 程序崩溃
- 检查输入PCM文件格式是否正确
- 确保是48kHz, 16位, 单声道

**问题2**: 输出文件为空
- 检查输入文件路径是否正确
- 确保有足够的磁盘空间

## 🔍 高级测试

### 性能测试
```cmd
# 测试处理时间
powershell "Measure-Command { .\rnnoise_demo.exe test_input.pcm output_denoised.pcm }"
```

### 批量测试
```cmd
# 如果有多个测试文件
for %%f in (*.pcm) do (
    rnnoise_demo.exe "%%f" "output_%%f"
)
```

### 内存使用检查
使用任务管理器监控程序运行时的内存使用情况。

## 📈 效果评估

### 主观评估
1. **清晰度**: 语音是否更清晰？
2. **噪声抑制**: 背景噪声是否减少？
3. **音质**: 是否有明显的失真？

### 客观指标
- **文件大小对比**: 输入vs输出
- **处理时间**: 实时性能评估
- **频谱分析**: 使用Audacity等工具

## 🎉 成功标志

如果看到以下情况，说明测试成功：
- ✅ 编译无错误警告
- ✅ 程序正常运行并退出
- ✅ 生成了 `output_denoised.pcm` 和 `output_denoised.wav`
- ✅ 输出音频可以正常播放
- ✅ 降噪效果明显

## 📞 需要帮助？

如果遇到问题：
1. **检查编译器安装**: `gcc --version`
2. **验证文件路径**: 确保所有文件都在正确位置
3. **查看错误信息**: 仔细阅读编译或运行时错误
4. **对比原始模型**: 先用原始模型测试，确保环境正常

## 🔄 恢复原始模型

如果需要恢复原始模型：
```cmd
copy src\rnnoise_data.c.backup src\rnnoise_data.c
```

---

**祝您测试成功！** 🎊

您的自定义RNNoise模型已经准备就绪，现在就看C语言实现的效果如何了！
