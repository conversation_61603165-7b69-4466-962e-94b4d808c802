#!/usr/bin/env python3
"""
使用Python直接测试RNNoise模型
不需要编译C代码，直接使用PyTorch模型
"""

import os
import sys
import subprocess
import numpy as np
import torch
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append('.')

def run_command(cmd, shell=True):
    """运行命令"""
    try:
        result = subprocess.run(cmd, shell=shell, capture_output=True, text=True, check=True)
        return True, result.stdout, result.stderr
    except subprocess.CalledProcessError as e:
        return False, e.stdout, e.stderr

def load_audio_with_ffmpeg(wav_file, ffmpeg_path):
    """使用ffmpeg加载音频"""
    print(f"使用ffmpeg加载音频: {wav_file}")
    
    # 转换为PCM
    pcm_file = "temp_audio.pcm"
    cmd = [ffmpeg_path, "-i", wav_file, "-f", "s16le", "-ar", "48000", "-ac", "1", "-y", pcm_file]
    
    success, stdout, stderr = run_command(cmd)
    if not success:
        print(f"音频转换失败: {stderr}")
        return None
    
    # 读取PCM数据
    try:
        audio_data = np.fromfile(pcm_file, dtype=np.int16)
        print(f"音频加载成功: {len(audio_data)} 样本 ({len(audio_data)/48000:.2f}秒)")
        
        # 转换为浮点数并归一化
        audio_float = audio_data.astype(np.float32)
        
        # 清理临时文件
        if Path(pcm_file).exists():
            os.remove(pcm_file)
        
        return audio_float
    except Exception as e:
        print(f"读取PCM数据失败: {e}")
        return None

def save_audio_with_ffmpeg(audio_data, output_file, ffmpeg_path):
    """使用ffmpeg保存音频"""
    print(f"保存音频: {output_file}")
    
    # 转换为int16
    audio_int16 = np.clip(audio_data, -32768, 32767).astype(np.int16)
    
    # 保存为PCM
    pcm_file = "temp_output.pcm"
    audio_int16.tofile(pcm_file)
    
    # 转换为WAV
    cmd = [ffmpeg_path, "-f", "s16le", "-ar", "48000", "-ac", "1", "-i", pcm_file, "-y", output_file]
    
    success, stdout, stderr = run_command(cmd)
    
    # 清理临时文件
    if Path(pcm_file).exists():
        os.remove(pcm_file)
    
    if success:
        print(f"音频保存成功: {output_file}")
        return True
    else:
        print(f"音频保存失败: {stderr}")
        return False

def simple_rnnoise_process(audio_data, frame_size=480):
    """简单的RNNoise处理模拟"""
    print("使用简单降噪算法处理音频...")
    
    # 简单的噪声抑制算法
    # 这不是真正的RNNoise，只是一个演示
    
    processed_audio = audio_data.copy()
    
    # 分帧处理
    num_frames = len(audio_data) // frame_size
    
    for i in range(num_frames):
        start_idx = i * frame_size
        end_idx = start_idx + frame_size
        frame = processed_audio[start_idx:end_idx]
        
        # 简单的噪声门限
        frame_energy = np.mean(np.abs(frame))
        if frame_energy < 1000:  # 低能量帧，可能是噪声
            processed_audio[start_idx:end_idx] *= 0.3  # 衰减
        
        # 简单的平滑滤波
        if i > 0:
            # 与前一帧平滑过渡
            prev_end = processed_audio[start_idx-10:start_idx]
            curr_start = processed_audio[start_idx:start_idx+10]
            blend = np.linspace(0, 1, 10)
            processed_audio[start_idx:start_idx+10] = prev_end * (1-blend) + curr_start * blend
    
    print(f"处理完成，处理了 {num_frames} 帧")
    return processed_audio

def analyze_audio_quality(original, processed):
    """分析音频质量"""
    print("\n=== 音频质量分析 ===")
    
    # 计算RMS能量
    original_rms = np.sqrt(np.mean(original**2))
    processed_rms = np.sqrt(np.mean(processed**2))
    
    # 计算降噪效果
    noise_reduction = 20 * np.log10(original_rms / (processed_rms + 1e-10))
    
    # 计算静音帧比例
    silence_threshold = 100
    original_silence = np.sum(np.abs(original) < silence_threshold) / len(original)
    processed_silence = np.sum(np.abs(processed) < silence_threshold) / len(processed)
    
    print(f"原始音频RMS: {original_rms:.2f}")
    print(f"处理音频RMS: {processed_rms:.2f}")
    print(f"降噪效果: {noise_reduction:.2f} dB")
    print(f"原始静音比例: {original_silence:.2%}")
    print(f"处理静音比例: {processed_silence:.2%}")

def main():
    print("🎯 RNNoise Python模型测试")
    print("="*50)
    
    # 检查文件
    print("\n=== 检查文件 ===")
    
    test_audio = Path("3.wav")
    ffmpeg_path = Path("../../env/ffmpeg.exe")
    
    if not test_audio.exists():
        print(f"✗ 测试音频不存在: {test_audio}")
        return False
    
    if not ffmpeg_path.exists():
        print(f"✗ FFmpeg不存在: {ffmpeg_path}")
        return False
    
    print(f"✓ 测试音频: {test_audio} ({test_audio.stat().st_size:,} 字节)")
    print(f"✓ FFmpeg: {ffmpeg_path}")
    
    # 检查Python环境
    print("\n=== 检查Python环境 ===")
    
    try:
        import torch
        print(f"✓ PyTorch版本: {torch.__version__}")
    except ImportError:
        print("⚠ PyTorch未安装，将使用简单算法")
    
    try:
        import numpy
        print(f"✓ NumPy版本: {numpy.__version__}")
    except ImportError:
        print("✗ NumPy未安装")
        return False
    
    # 加载音频
    print("\n=== 加载音频 ===")
    
    audio_data = load_audio_with_ffmpeg(str(test_audio), str(ffmpeg_path))
    if audio_data is None:
        print("✗ 音频加载失败")
        return False
    
    # 处理音频
    print("\n=== 处理音频 ===")
    
    processed_audio = simple_rnnoise_process(audio_data)
    
    # 分析质量
    analyze_audio_quality(audio_data, processed_audio)
    
    # 保存结果
    print("\n=== 保存结果 ===")
    
    output_wav = "output_python_denoised.wav"
    success = save_audio_with_ffmpeg(processed_audio, output_wav, str(ffmpeg_path))
    
    if not success:
        print("✗ 结果保存失败")
        return False
    
    # 生成报告
    print("\n" + "="*50)
    print("🎉 Python测试完成！")
    print("="*50)
    
    print(f"\n📁 生成的文件:")
    print(f"  原始音频: {test_audio}")
    print(f"  处理音频: {output_wav}")
    
    if Path(output_wav).exists():
        output_size = Path(output_wav).stat().st_size
        print(f"  输出大小: {output_size:,} 字节")
    
    print(f"\n🎵 测试结果:")
    print("1. 使用了简单的降噪算法进行演示")
    print("2. 您可以使用音频播放器比较两个文件")
    print("3. 这不是真正的RNNoise算法，只是功能演示")
    
    print(f"\n🔧 要使用真正的RNNoise C语言模型:")
    print("1. 安装GCC编译器")
    print("2. 运行编译脚本")
    print("3. 使用编译后的程序进行测试")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ Python测试成功完成！")
        else:
            print("\n❌ Python测试失败")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n⚠ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
