#!/usr/bin/env python3
"""
专门针对您的RNNoise模型的测试脚本
模型位置: D:\RNNoise\rnnoise-plus-main\torch\rnnoise\rnnoise_c
测试音频: D:\RNNoise\rnnoise-plus-main\torch\rnnoise\3.wav
"""

import os
import sys
import subprocess
import platform
import shutil
from pathlib import Path

class YourModelTester:
    """您的RNNoise模型测试器"""
    
    def __init__(self):
        self.base_dir = Path(".")
        self.model_dir = Path("torch/rnnoise/rnnoise_c")
        self.test_audio = Path("torch/rnnoise/3.wav")
        self.is_windows = platform.system().lower() == 'windows'
        self.exe_ext = '.exe' if self.is_windows else ''
        
        print(f"操作系统: {platform.system()}")
        print(f"工作目录: {os.getcwd()}")
        print(f"模型目录: {self.model_dir}")
        print(f"测试音频: {self.test_audio}")
    
    def check_files(self):
        """检查必要文件是否存在"""
        print("\n=== 检查文件 ===")
        
        files_to_check = [
            ("模型头文件", self.model_dir / "rnnoise_data.h"),
            ("模型数据文件", self.model_dir / "rnnoise_data.c"),
            ("测试音频", self.test_audio),
            ("RNNoise头文件", "include/rnnoise.h"),
            ("示例程序", "examples/rnnoise_demo.c"),
        ]
        
        all_exist = True
        for name, path in files_to_check:
            if path.exists():
                size = path.stat().st_size if path.is_file() else "目录"
                print(f"✓ {name}: {path} ({size} 字节)")
            else:
                print(f"✗ {name}: {path} (不存在)")
                all_exist = False
        
        return all_exist
    
    def prepare_custom_model(self):
        """准备自定义模型文件"""
        print("\n=== 准备自定义模型 ===")
        
        # 复制自定义模型文件到src目录
        src_dir = Path("src")
        
        custom_h = self.model_dir / "rnnoise_data.h"
        custom_c = self.model_dir / "rnnoise_data.c"
        
        if custom_h.exists() and custom_c.exists():
            # 备份原始文件
            original_c = src_dir / "rnnoise_data.c"
            if original_c.exists():
                shutil.copy2(original_c, src_dir / "rnnoise_data.c.backup")
                print("✓ 备份原始模型文件")
            
            # 复制自定义模型文件
            shutil.copy2(custom_h, src_dir / "rnnoise_data.h")
            shutil.copy2(custom_c, src_dir / "rnnoise_data.c")
            print("✓ 复制自定义模型文件到src目录")
            return True
        else:
            print("✗ 自定义模型文件不完整")
            return False
    
    def compile_programs(self):
        """编译测试程序"""
        print("\n=== 编译程序 ===")
        
        # 编译参数
        cflags = ["-O3", "-Wall", "-Wextra", "-std=c99"]
        includes = ["-Iinclude", "-Isrc"]
        libs = ["-lm"]
        
        # 源文件
        sources = [
            "src/denoise.c",
            "src/rnn.c", 
            "src/pitch.c",
            "src/kiss_fft.c",
            "src/celt_lpc.c",
            "src/nnet.c",
            "src/nnet_default.c",
            "src/parse_lpcnet_weights.c",
            "src/rnnoise_data.c",
            "src/rnnoise_tables.c"
        ]
        
        # 检查源文件是否存在
        missing_sources = [s for s in sources if not Path(s).exists()]
        if missing_sources:
            print(f"✗ 缺少源文件: {missing_sources}")
            return False
        
        try:
            # 编译对象文件
            print("编译对象文件...")
            compile_cmd = ["gcc"] + cflags + includes + ["-c"] + sources
            result = subprocess.run(compile_cmd, capture_output=True, text=True, check=True)
            print("✓ 对象文件编译成功")
            
            # 编译自定义测试程序
            print("编译自定义测试程序...")
            test_cmd = ["gcc"] + cflags + includes + ["-o", f"test_your_model{self.exe_ext}", 
                       "test_custom_model.c"] + [f.replace('.c', '.o') for f in sources if f.endswith('.c')] + libs
            result = subprocess.run(test_cmd, capture_output=True, text=True, check=True)
            print(f"✓ 自定义测试程序编译成功: test_your_model{self.exe_ext}")
            
            # 编译原始示例程序
            print("编译原始示例程序...")
            demo_cmd = ["gcc"] + cflags + includes + ["-o", f"rnnoise_demo{self.exe_ext}",
                       "examples/rnnoise_demo.c"] + [f.replace('.c', '.o') for f in sources if f.endswith('.c')] + libs
            result = subprocess.run(demo_cmd, capture_output=True, text=True, check=True)
            print(f"✓ 原始示例程序编译成功: rnnoise_demo{self.exe_ext}")
            
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"✗ 编译失败: {e}")
            print(f"错误输出: {e.stderr}")
            return False
    
    def convert_audio_to_pcm(self):
        """将WAV音频转换为PCM格式"""
        print("\n=== 转换音频格式 ===")
        
        # 查找ffmpeg
        ffmpeg_paths = ["env/ffmpeg.exe", "ffmpeg.exe", "ffmpeg"]
        ffmpeg = None
        
        for path in ffmpeg_paths:
            if Path(path).exists() or shutil.which(path):
                ffmpeg = path
                break
        
        if not ffmpeg:
            print("✗ 未找到ffmpeg，请安装ffmpeg")
            return False
        
        print(f"使用ffmpeg: {ffmpeg}")
        
        # 转换命令
        input_wav = self.test_audio
        output_pcm = "test_input.pcm"
        
        cmd = [
            ffmpeg,
            "-i", str(input_wav),
            "-f", "s16le",  # 16位小端格式
            "-ar", "48000",  # 48kHz采样率
            "-ac", "1",      # 单声道
            "-y",            # 覆盖输出文件
            output_pcm
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            print(f"✓ 音频转换成功: {input_wav} -> {output_pcm}")
            return output_pcm
        except subprocess.CalledProcessError as e:
            print(f"✗ 音频转换失败: {e.stderr}")
            return None
    
    def run_tests(self, input_pcm):
        """运行降噪测试"""
        print("\n=== 运行降噪测试 ===")
        
        tests = [
            ("自定义模型", f"test_your_model{self.exe_ext}", "output_custom.pcm"),
            ("原始模型", f"rnnoise_demo{self.exe_ext}", "output_original.pcm")
        ]
        
        results = []
        
        for name, program, output in tests:
            if not Path(program).exists():
                print(f"✗ {name}程序不存在: {program}")
                continue
            
            print(f"\n--- 测试 {name} ---")
            
            cmd = [f"./{program}", input_pcm, output]
            
            try:
                import time
                start_time = time.time()
                result = subprocess.run(cmd, capture_output=True, text=True, check=True)
                end_time = time.time()
                
                processing_time = end_time - start_time
                output_size = Path(output).stat().st_size if Path(output).exists() else 0
                
                print(f"✓ {name}测试成功")
                print(f"  处理时间: {processing_time:.2f}秒")
                print(f"  输出大小: {output_size} 字节")
                
                results.append({
                    'name': name,
                    'success': True,
                    'time': processing_time,
                    'output': output,
                    'size': output_size
                })
                
            except subprocess.CalledProcessError as e:
                print(f"✗ {name}测试失败: {e}")
                print(f"错误输出: {e.stderr}")
                results.append({
                    'name': name,
                    'success': False,
                    'error': str(e)
                })
        
        return results
    
    def convert_results_to_wav(self, results):
        """将PCM结果转换为WAV格式"""
        print("\n=== 转换结果为WAV ===")
        
        # 查找ffmpeg
        ffmpeg_paths = ["env/ffmpeg.exe", "ffmpeg.exe", "ffmpeg"]
        ffmpeg = None
        
        for path in ffmpeg_paths:
            if Path(path).exists() or shutil.which(path):
                ffmpeg = path
                break
        
        if not ffmpeg:
            print("✗ 未找到ffmpeg，跳过WAV转换")
            return
        
        for result in results:
            if not result['success']:
                continue
            
            pcm_file = result['output']
            wav_file = pcm_file.replace('.pcm', '.wav')
            
            cmd = [
                ffmpeg,
                "-f", "s16le",   # 输入格式
                "-ar", "48000",  # 采样率
                "-ac", "1",      # 单声道
                "-i", pcm_file,
                "-y",            # 覆盖输出
                wav_file
            ]
            
            try:
                subprocess.run(cmd, capture_output=True, text=True, check=True)
                print(f"✓ 转换成功: {pcm_file} -> {wav_file}")
            except subprocess.CalledProcessError as e:
                print(f"✗ 转换失败 {pcm_file}: {e.stderr}")
    
    def run_full_test(self):
        """运行完整测试"""
        print("🎯 您的RNNoise模型测试开始")
        print("="*50)
        
        # 步骤1: 检查文件
        if not self.check_files():
            print("\n❌ 必要文件缺失，测试中止")
            return False
        
        # 步骤2: 准备自定义模型
        if not self.prepare_custom_model():
            print("\n❌ 自定义模型准备失败，测试中止")
            return False
        
        # 步骤3: 编译程序
        if not self.compile_programs():
            print("\n❌ 程序编译失败，测试中止")
            return False
        
        # 步骤4: 转换音频
        input_pcm = self.convert_audio_to_pcm()
        if not input_pcm:
            print("\n❌ 音频转换失败，测试中止")
            return False
        
        # 步骤5: 运行测试
        results = self.run_tests(input_pcm)
        if not any(r['success'] for r in results):
            print("\n❌ 所有测试都失败了")
            return False
        
        # 步骤6: 转换结果
        self.convert_results_to_wav(results)
        
        # 步骤7: 总结
        print("\n" + "="*50)
        print("🎉 测试完成！")
        print("="*50)
        
        print("\n📊 测试结果:")
        for result in results:
            if result['success']:
                print(f"✓ {result['name']}: {result['output']} ({result['size']} 字节, {result['time']:.2f}秒)")
            else:
                print(f"✗ {result['name']}: 失败")
        
        print("\n📁 生成的文件:")
        output_files = [
            "test_input.pcm",
            "output_custom.pcm", "output_custom.wav",
            "output_original.pcm", "output_original.wav"
        ]
        
        for file in output_files:
            if Path(file).exists():
                size = Path(file).stat().st_size
                print(f"  {file} ({size} 字节)")
        
        print("\n🎵 下一步:")
        print("1. 使用音频播放器试听 output_custom.wav 和 output_original.wav")
        print("2. 比较您的自定义模型和原始模型的降噪效果")
        print("3. 如果效果不理想，可以调整训练参数重新训练")
        
        return True

def main():
    tester = YourModelTester()
    success = tester.run_full_test()
    
    if success:
        print("\n🎊 恭喜！您的RNNoise C语言模型测试成功！")
    else:
        print("\n😞 测试过程中遇到了问题，请检查错误信息")
        sys.exit(1)

if __name__ == "__main__":
    main()
