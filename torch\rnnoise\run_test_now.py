#!/usr/bin/env python3
"""
立即运行RNNoise C语言模型测试
"""

import os
import sys
import subprocess
import shutil
import time
from pathlib import Path

def run_command(cmd, cwd=None, shell=True):
    """运行命令并返回结果"""
    try:
        print(f"执行命令: {' '.join(cmd) if isinstance(cmd, list) else cmd}")
        result = subprocess.run(cmd, cwd=cwd, shell=shell, 
                              capture_output=True, text=True, check=True)
        return True, result.stdout, result.stderr
    except subprocess.CalledProcessError as e:
        return False, e.stdout, e.stderr

def main():
    print("🎯 RNNoise C语言模型测试开始")
    print("="*50)
    
    # 当前目录应该是 torch/rnnoise
    current_dir = Path(".")
    root_dir = Path("../..")
    
    print(f"当前目录: {os.getcwd()}")
    print(f"项目根目录: {root_dir.resolve()}")
    
    # 步骤1: 检查文件
    print("\n=== 步骤1: 检查文件 ===")
    
    files_to_check = [
        ("C模型头文件", "rnnoise_c/rnnoise_data.h"),
        ("C模型数据文件", "rnnoise_c/rnnoise_data.c"),
        ("测试音频", "3.wav"),
        ("FFmpeg", "../../env/ffmpeg.exe"),
        ("RNNoise头文件", "../../include/rnnoise.h"),
        ("示例程序", "../../examples/rnnoise_demo.c")
    ]
    
    all_files_exist = True
    for name, path in files_to_check:
        file_path = Path(path)
        if file_path.exists():
            size = file_path.stat().st_size
            print(f"✓ {name}: {path} ({size:,} 字节)")
        else:
            print(f"✗ {name}: {path} (不存在)")
            all_files_exist = False
    
    if not all_files_exist:
        print("\n❌ 部分必要文件缺失，测试中止")
        return False
    
    # 步骤2: 检查编译器
    print("\n=== 步骤2: 检查编译器 ===")
    
    compilers = ["gcc", "clang"]
    compiler = None
    
    for comp in compilers:
        success, stdout, stderr = run_command([comp, "--version"])
        if success:
            print(f"✓ 找到编译器: {comp}")
            print(f"  版本: {stdout.split()[0] if stdout else 'Unknown'}")
            compiler = comp
            break
    
    if not compiler:
        print("✗ 未找到编译器")
        print("请安装GCC或Clang编译器")
        print("Windows推荐: https://www.mingw-w64.org/")
        return False
    
    # 步骤3: 准备编译环境
    print("\n=== 步骤3: 准备编译环境 ===")
    
    # 切换到项目根目录
    os.chdir(root_dir)
    print(f"切换到项目根目录: {os.getcwd()}")
    
    # 备份原始模型文件
    original_c = Path("src/rnnoise_data.c")
    if original_c.exists():
        backup_c = Path("src/rnnoise_data.c.backup")
        shutil.copy2(original_c, backup_c)
        print("✓ 备份原始模型文件")
    
    # 复制自定义模型文件
    custom_h = Path("torch/rnnoise/rnnoise_c/rnnoise_data.h")
    custom_c = Path("torch/rnnoise/rnnoise_c/rnnoise_data.c")
    
    if custom_h.exists() and custom_c.exists():
        shutil.copy2(custom_h, "src/rnnoise_data.h")
        shutil.copy2(custom_c, "src/rnnoise_data.c")
        print("✓ 复制自定义模型文件到src目录")
    else:
        print("✗ 自定义模型文件不完整")
        return False
    
    # 步骤4: 编译程序
    print("\n=== 步骤4: 编译程序 ===")
    
    # 源文件列表
    sources = [
        "src/denoise.c",
        "src/rnn.c",
        "src/pitch.c",
        "src/kiss_fft.c", 
        "src/celt_lpc.c",
        "src/nnet.c",
        "src/nnet_default.c",
        "src/parse_lpcnet_weights.c",
        "src/rnnoise_data.c",
        "src/rnnoise_tables.c"
    ]
    
    # 检查源文件
    missing_sources = [s for s in sources if not Path(s).exists()]
    if missing_sources:
        print(f"✗ 缺少源文件: {missing_sources}")
        return False
    
    # 编译对象文件
    print("编译对象文件...")
    compile_cmd = [compiler, "-O3", "-Wall", "-std=c99", "-Iinclude", "-Isrc", "-c"] + sources
    success, stdout, stderr = run_command(compile_cmd)
    
    if not success:
        print(f"✗ 编译失败: {stderr}")
        return False
    
    print("✓ 对象文件编译成功")
    
    # 编译示例程序
    print("编译示例程序...")
    objects = [s.replace('.c', '.o').replace('src/', '') for s in sources]
    demo_cmd = [compiler, "-O3", "-Wall", "-std=c99", "-Iinclude", "-Isrc", 
               "-o", "rnnoise_demo.exe", "examples/rnnoise_demo.c"] + objects + ["-lm"]
    
    success, stdout, stderr = run_command(demo_cmd)
    
    if not success:
        print(f"✗ 示例程序编译失败: {stderr}")
        return False
    
    print("✓ 示例程序编译成功: rnnoise_demo.exe")
    
    # 步骤5: 转换音频格式
    print("\n=== 步骤5: 转换音频格式 ===")
    
    ffmpeg = "env/ffmpeg.exe"
    input_wav = "torch/rnnoise/3.wav"
    input_pcm = "test_input.pcm"
    
    convert_cmd = [ffmpeg, "-i", input_wav, "-f", "s16le", "-ar", "48000", "-ac", "1", "-y", input_pcm]
    success, stdout, stderr = run_command(convert_cmd)
    
    if not success:
        print(f"✗ 音频转换失败: {stderr}")
        return False
    
    input_size = Path(input_pcm).stat().st_size
    duration = input_size / (48000 * 2)  # 48kHz, 16-bit
    print(f"✓ 音频转换成功: {input_pcm} ({input_size:,} 字节, {duration:.2f}秒)")
    
    # 步骤6: 运行降噪测试
    print("\n=== 步骤6: 运行降噪测试 ===")
    
    output_pcm = "output_denoised.pcm"
    denoise_cmd = ["./rnnoise_demo.exe", input_pcm, output_pcm]
    
    print("开始降噪处理...")
    start_time = time.time()
    success, stdout, stderr = run_command(denoise_cmd)
    end_time = time.time()
    
    if not success:
        print(f"✗ 降噪测试失败: {stderr}")
        return False
    
    processing_time = end_time - start_time
    output_size = Path(output_pcm).stat().st_size if Path(output_pcm).exists() else 0
    
    print(f"✓ 降噪测试成功")
    print(f"  处理时间: {processing_time:.2f}秒")
    print(f"  输出大小: {output_size:,} 字节")
    print(f"  实时倍数: {duration/processing_time:.2f}x" if processing_time > 0 else "")
    
    # 步骤7: 转换结果为WAV
    print("\n=== 步骤7: 转换结果为WAV ===")
    
    output_wav = "output_denoised.wav"
    wav_cmd = [ffmpeg, "-f", "s16le", "-ar", "48000", "-ac", "1", "-i", output_pcm, "-y", output_wav]
    
    success, stdout, stderr = run_command(wav_cmd)
    
    if not success:
        print(f"✗ WAV转换失败: {stderr}")
        return False
    
    wav_size = Path(output_wav).stat().st_size
    print(f"✓ WAV转换成功: {output_wav} ({wav_size:,} 字节)")
    
    # 步骤8: 生成测试报告
    print("\n" + "="*50)
    print("🎉 测试完成！")
    print("="*50)
    
    print(f"\n📊 测试结果:")
    print(f"  编译器: {compiler}")
    print(f"  处理时间: {processing_time:.2f}秒")
    print(f"  音频时长: {duration:.2f}秒")
    print(f"  实时性能: {duration/processing_time:.2f}x 实时" if processing_time > 0 else "")
    
    print(f"\n📁 生成的文件:")
    result_files = [
        ("输入PCM", input_pcm),
        ("输出PCM", output_pcm), 
        ("输出WAV", output_wav)
    ]
    
    for name, file in result_files:
        if Path(file).exists():
            size = Path(file).stat().st_size
            print(f"  {name}: {file} ({size:,} 字节)")
    
    print(f"\n🎵 音频对比:")
    print(f"  原始音频: torch/rnnoise/3.wav")
    print(f"  降噪音频: {output_wav}")
    
    print(f"\n🎯 下一步:")
    print("1. 使用音频播放器试听两个音频文件")
    print("2. 比较降噪效果和音质")
    print("3. 如果效果不理想，可以调整训练参数重新训练模型")
    
    # 清理临时文件
    print(f"\n🧹 清理临时文件...")
    temp_files = [f for f in os.listdir('.') if f.endswith('.o')]
    for temp_file in temp_files:
        os.remove(temp_file)
    print(f"✓ 清理了 {len(temp_files)} 个临时文件")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎊 恭喜！您的RNNoise C语言模型测试成功！")
        else:
            print("\n😞 测试过程中遇到了问题")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n⚠ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
        sys.exit(1)
