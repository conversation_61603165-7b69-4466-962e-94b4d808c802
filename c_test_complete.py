#!/usr/bin/env python3
"""
完整的C语言RNNoise测试脚本
自动处理编译器安装、编译和测试
"""

import os
import sys
import subprocess
import shutil
import urllib.request
import zipfile
from pathlib import Path

def run_command(cmd, cwd=None, shell=True):
    """运行命令"""
    try:
        print(f"执行: {' '.join(cmd) if isinstance(cmd, list) else cmd}")
        result = subprocess.run(cmd, cwd=cwd, shell=shell, 
                              capture_output=True, text=True, check=True)
        return True, result.stdout, result.stderr
    except subprocess.CalledProcessError as e:
        return False, e.stdout, e.stderr

def check_compiler():
    """检查编译器"""
    print("\n=== 检查编译器 ===")
    
    # 检查系统编译器
    compilers = ["gcc", "clang", "cl"]
    
    for compiler in compilers:
        success, stdout, stderr = run_command([compiler, "--version"])
        if success:
            print(f"✓ 找到系统编译器: {compiler}")
            return compiler
    
    # 检查env目录中的编译器
    env_compilers = [
        "env/mingw64/bin/gcc.exe",
        "env/bin/gcc.exe",
        "env/gcc.exe"
    ]
    
    for compiler_path in env_compilers:
        if Path(compiler_path).exists():
            print(f"✓ 找到本地编译器: {compiler_path}")
            return compiler_path
    
    print("✗ 未找到编译器")
    return None

def download_portable_gcc():
    """下载便携式GCC"""
    print("\n=== 下载便携式GCC ===")
    
    env_dir = Path("env")
    env_dir.mkdir(exist_ok=True)
    
    # 使用WinLibs的便携式MinGW
    gcc_url = "https://github.com/brechtsanders/winlibs_mingw/releases/download/13.2.0-16.0.6-11.0.0-msvcrt-r1/winlibs-x86_64-posix-seh-gcc-13.2.0-mingw-w64msvcrt-11.0.0-r1.zip"
    gcc_zip = env_dir / "mingw.zip"
    
    print(f"下载URL: {gcc_url}")
    
    try:
        print("正在下载MinGW...")
        urllib.request.urlretrieve(gcc_url, gcc_zip)
        print("✓ 下载完成")
        
        print("正在解压...")
        with zipfile.ZipFile(gcc_zip, 'r') as zip_ref:
            zip_ref.extractall(env_dir)
        print("✓ 解压完成")
        
        # 清理下载文件
        gcc_zip.unlink()
        
        # 查找gcc.exe
        gcc_exe = env_dir / "mingw64" / "bin" / "gcc.exe"
        if gcc_exe.exists():
            print(f"✓ GCC安装成功: {gcc_exe}")
            return str(gcc_exe)
        else:
            print("✗ 解压后未找到gcc.exe")
            return None
            
    except Exception as e:
        print(f"✗ 下载失败: {e}")
        return None

def prepare_c_model():
    """准备C语言模型文件"""
    print("\n=== 准备C语言模型文件 ===")
    
    # 检查自定义模型文件
    custom_h = Path("torch/rnnoise/rnnoise_c/rnnoise_data.h")
    custom_c = Path("torch/rnnoise/rnnoise_c/rnnoise_data.c")
    
    if not (custom_h.exists() and custom_c.exists()):
        print("✗ 自定义模型文件不存在")
        return False
    
    print(f"✓ 找到自定义模型文件:")
    print(f"  头文件: {custom_h} ({custom_h.stat().st_size:,} 字节)")
    print(f"  数据文件: {custom_c} ({custom_c.stat().st_size:,} 字节)")
    
    # 备份原始文件
    original_c = Path("src/rnnoise_data.c")
    if original_c.exists():
        backup_c = Path("src/rnnoise_data.c.backup")
        shutil.copy2(original_c, backup_c)
        print("✓ 备份原始模型文件")
    
    # 复制自定义模型文件
    shutil.copy2(custom_h, "src/rnnoise_data.h")
    shutil.copy2(custom_c, "src/rnnoise_data.c")
    print("✓ 复制自定义模型文件到src目录")
    
    return True

def compile_rnnoise(compiler):
    """编译RNNoise程序"""
    print(f"\n=== 编译RNNoise程序 ===")
    print(f"使用编译器: {compiler}")
    
    # 源文件列表
    sources = [
        "src/denoise.c",
        "src/rnn.c",
        "src/pitch.c",
        "src/kiss_fft.c",
        "src/celt_lpc.c", 
        "src/nnet.c",
        "src/nnet_default.c",
        "src/parse_lpcnet_weights.c",
        "src/rnnoise_data.c",
        "src/rnnoise_tables.c"
    ]
    
    # 检查源文件
    missing_sources = [s for s in sources if not Path(s).exists()]
    if missing_sources:
        print(f"✗ 缺少源文件: {missing_sources}")
        return False
    
    print(f"✓ 所有源文件存在 ({len(sources)} 个)")
    
    # 编译参数
    cflags = ["-O3", "-Wall", "-std=c99"]
    includes = ["-Iinclude", "-Isrc"]
    libs = ["-lm"] if not compiler.endswith('.exe') else []
    
    # 编译对象文件
    print("编译对象文件...")
    compile_cmd = [compiler] + cflags + includes + ["-c"] + sources
    success, stdout, stderr = run_command(compile_cmd)
    
    if not success:
        print(f"✗ 编译失败:")
        print(stderr)
        return False
    
    print("✓ 对象文件编译成功")
    
    # 编译示例程序
    print("编译示例程序...")
    objects = [s.replace('.c', '.o').replace('src/', '') for s in sources]
    demo_cmd = [compiler] + cflags + includes + ["-o", "rnnoise_demo.exe", 
               "examples/rnnoise_demo.c"] + objects + libs
    
    success, stdout, stderr = run_command(demo_cmd)
    
    if not success:
        print(f"✗ 示例程序编译失败:")
        print(stderr)
        return False
    
    print("✓ 示例程序编译成功: rnnoise_demo.exe")
    return True

def test_audio_processing():
    """测试音频处理"""
    print(f"\n=== 测试音频处理 ===")
    
    # 检查测试音频
    test_audio = Path("torch/rnnoise/3.wav")
    if not test_audio.exists():
        print(f"✗ 测试音频不存在: {test_audio}")
        return False
    
    print(f"✓ 测试音频: {test_audio} ({test_audio.stat().st_size:,} 字节)")
    
    # 检查ffmpeg
    ffmpeg = Path("env/ffmpeg.exe")
    if not ffmpeg.exists():
        print(f"✗ FFmpeg不存在: {ffmpeg}")
        return False
    
    print(f"✓ FFmpeg: {ffmpeg}")
    
    # 转换音频为PCM
    print("转换音频为PCM格式...")
    input_pcm = "test_input.pcm"
    convert_cmd = [str(ffmpeg), "-i", str(test_audio), "-f", "s16le", 
                  "-ar", "48000", "-ac", "1", "-y", input_pcm]
    
    success, stdout, stderr = run_command(convert_cmd)
    if not success:
        print(f"✗ 音频转换失败: {stderr}")
        return False
    
    input_size = Path(input_pcm).stat().st_size
    duration = input_size / (48000 * 2)
    print(f"✓ 音频转换成功: {input_pcm} ({input_size:,} 字节, {duration:.2f}秒)")
    
    # 运行降噪
    print("运行降噪处理...")
    output_pcm = "output_c_denoised.pcm"
    denoise_cmd = ["./rnnoise_demo.exe", input_pcm, output_pcm]
    
    import time
    start_time = time.time()
    success, stdout, stderr = run_command(denoise_cmd)
    end_time = time.time()
    
    if not success:
        print(f"✗ 降噪处理失败: {stderr}")
        return False
    
    processing_time = end_time - start_time
    output_size = Path(output_pcm).stat().st_size if Path(output_pcm).exists() else 0
    
    print(f"✓ 降噪处理成功")
    print(f"  处理时间: {processing_time:.2f}秒")
    print(f"  输出大小: {output_size:,} 字节")
    print(f"  实时倍数: {duration/processing_time:.2f}x" if processing_time > 0 else "")
    
    # 转换结果为WAV
    print("转换结果为WAV格式...")
    output_wav = "output_c_denoised.wav"
    wav_cmd = [str(ffmpeg), "-f", "s16le", "-ar", "48000", "-ac", "1", 
              "-i", output_pcm, "-y", output_wav]
    
    success, stdout, stderr = run_command(wav_cmd)
    if not success:
        print(f"✗ WAV转换失败: {stderr}")
        return False
    
    wav_size = Path(output_wav).stat().st_size
    print(f"✓ WAV转换成功: {output_wav} ({wav_size:,} 字节)")
    
    return True, processing_time, duration

def cleanup_temp_files():
    """清理临时文件"""
    print(f"\n=== 清理临时文件 ===")
    
    temp_files = list(Path(".").glob("*.o")) + ["test_input.pcm"]
    
    for temp_file in temp_files:
        try:
            if Path(temp_file).exists():
                Path(temp_file).unlink()
        except:
            pass
    
    print(f"✓ 清理了 {len(temp_files)} 个临时文件")

def main():
    print("🎯 RNNoise C语言完整测试")
    print("="*50)
    
    try:
        # 步骤1: 检查编译器
        compiler = check_compiler()
        
        # 如果没有编译器，尝试下载
        if not compiler:
            print("正在下载便携式编译器...")
            compiler = download_portable_gcc()
            
            if not compiler:
                print("\n❌ 无法获取编译器")
                print("请手动安装MinGW或GCC编译器")
                return False
        
        # 步骤2: 准备模型文件
        if not prepare_c_model():
            print("\n❌ 模型文件准备失败")
            return False
        
        # 步骤3: 编译程序
        if not compile_rnnoise(compiler):
            print("\n❌ 程序编译失败")
            return False
        
        # 步骤4: 测试音频处理
        result = test_audio_processing()
        if not result:
            print("\n❌ 音频处理测试失败")
            return False
        
        success, processing_time, duration = result
        
        # 步骤5: 清理临时文件
        cleanup_temp_files()
        
        # 生成报告
        print("\n" + "="*50)
        print("🎉 C语言测试完成！")
        print("="*50)
        
        print(f"\n📊 测试结果:")
        print(f"  编译器: {compiler}")
        print(f"  处理时间: {processing_time:.2f}秒")
        print(f"  音频时长: {duration:.2f}秒")
        print(f"  实时性能: {duration/processing_time:.2f}x 实时")
        
        print(f"\n📁 生成的文件:")
        result_files = [
            ("C语言程序", "rnnoise_demo.exe"),
            ("输出PCM", "output_c_denoised.pcm"),
            ("输出WAV", "output_c_denoised.wav")
        ]
        
        for name, file in result_files:
            if Path(file).exists():
                size = Path(file).stat().st_size
                print(f"  {name}: {file} ({size:,} 字节)")
        
        print(f"\n🎵 音频对比:")
        print(f"  原始音频: torch/rnnoise/3.wav")
        print(f"  C语言降噪: output_c_denoised.wav")
        print(f"  Python降噪: torch/rnnoise/output_pytorch_rnnoise.wav")
        
        print(f"\n🎯 测试成功！")
        print("1. ✅ 成功编译了您的自定义RNNoise C语言模型")
        print("2. ✅ 成功处理了测试音频")
        print("3. 🎧 请使用音频播放器比较不同版本的效果")
        
        return True
        
    except KeyboardInterrupt:
        print("\n\n⚠ 测试被用户中断")
        return False
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎊 恭喜！您的RNNoise C语言模型测试成功！")
    else:
        print("\n😞 测试失败，请检查错误信息")
        sys.exit(1)
